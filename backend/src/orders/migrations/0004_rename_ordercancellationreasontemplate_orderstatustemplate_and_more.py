# Generated by Django 5.2.4 on 2025-08-09 22:40

import core.utils
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('merchants', '0002_alter_merchant_options_alter_office_options_and_more'),
        ('orders', '0003_alter_order_code'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RenameModel(
            old_name='OrderCancellationReasonTemplate',
            new_name='OrderStatusTemplate',
        ),
        migrations.AlterModelOptions(
            name='orderstatustemplate',
            options={'verbose_name': 'Order Status Template', 'verbose_name_plural': 'Order Status Templates'},
        ),
        migrations.RemoveField(
            model_name='order',
            name='code',
        ),
        migrations.AlterModelTable(
            name='orderstatustemplate',
            table='order_status_templates',
        ),
        migrations.CreateModel(
            name='OrdersSheet',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders_sheets', to=settings.AUTH_USER_MODEL)),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders_sheets', to='merchants.office')),
            ],
            options={
                'verbose_name': 'Orders Sheet',
                'verbose_name_plural': 'Orders Sheets',
                'db_table': 'orders_sheets',
            },
        ),
        migrations.AddField(
            model_name='order',
            name='orders_sheet',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='orders.orderssheet'),
        ),
    ]
