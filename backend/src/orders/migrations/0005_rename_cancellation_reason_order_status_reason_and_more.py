# Generated by Django 5.2.4 on 2025-08-09 23:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('merchants', '0002_alter_merchant_options_alter_office_options_and_more'),
        ('orders', '0004_rename_ordercancellationreasontemplate_orderstatustemplate_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='order',
            old_name='cancellation_reason',
            new_name='status_reason',
        ),
        migrations.RenameField(
            model_name='order',
            old_name='cancellation_reason_template',
            new_name='status_template',
        ),
        migrations.RenameField(
            model_name='orderstatustemplate',
            old_name='description',
            new_name='reason_template_text',
        ),
        migrations.AlterField(
            model_name='orderstatustemplate',
            name='office',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='order_status_templates', to='merchants.office'),
        ),
    ]
