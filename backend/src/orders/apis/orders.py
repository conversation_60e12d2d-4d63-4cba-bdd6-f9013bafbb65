from typing import List

from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Q
from django.http import HttpRequest
from django.utils import timezone

from accounts.models import Role, User
from merchants.models import Office

from ..models import (
    Company,
    OrderStatusTemplate,
    OrdersSheet,
    Order,
    OrderStatusHistory,
    OrderAssigneeHistory,
    OrderHandlingStatus,
)
from ..schemas import (
    CompanySchema,
    OrderSchema,
    CreateOrderSchema,
    UpdateOrderSchema,
    OrderListResponseSchema,
    BulkCreateOrderResponseSchema,
)
from core.middleware import AuthKey
from core.utils import HttpException

from . import router


# Order endpoints
# GET /api/orders/
@router.get("/", auth=AuthKey, response=OrderListResponseSchema)
def list_orders(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    office_id: str | None = None,
    status: str | None = None,
    assigned_to_id: str | None = None,
    customer_company_id: str | None = None,
    search: str | None = None,
    created_at_from: str | None = None,
    created_at_to: str | None = None,
    assigned_to_isnull: bool = False,
):
    # Check if user has permission to list orders
    if request.user.role not in [Role.ADMIN, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpException(403, "You don't have permission to list orders")

    # Build queryset based on user role
    if request.user.role == Role.ADMIN:
        orders = Order.objects.all()
    else:
        # Managers and delivery agents can only see orders in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        orders = Order.objects.filter(office_id__in=user_offices)

    # Apply filters
    if office_id:
        orders = orders.filter(office_id=office_id)
    if status:
        orders = orders.filter(handling_status=status)
    if assigned_to_id:
        orders = orders.filter(assigned_to_id=assigned_to_id)
    if customer_company_id:
        orders = orders.filter(customer_company_id=customer_company_id)
    if search:
        orders = orders.filter(
            Q(code__icontains=search)
            | Q(customer_name__icontains=search)
            | Q(customer_phone__icontains=search)
            | Q(customer_address__icontains=search)
        )
    if created_at_from:
        orders = orders.filter(created_at__gte=created_at_from)
    if created_at_to:
        orders = orders.filter(created_at__lte=created_at_to)
    if assigned_to_isnull:
        orders = orders.filter(assigned_to_id__isnull=True)
    orders = orders.order_by("-created_at")

    paginator = Paginator(orders, page_size)
    page_obj = paginator.get_page(page)

    # Create order schemas with company details
    order_schemas = []
    for order in page_obj:
        order_data = OrderSchema.from_orm(order)
        if order.customer_company:
            order_data.customer_company = CompanySchema.from_orm(order.customer_company)
        order_schemas.append(order_data)

    return OrderListResponseSchema(
        orders=order_schemas,
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/orders/
@router.post("/", auth=AuthKey, response=OrderSchema)
def create_order(request: HttpRequest, data: CreateOrderSchema) -> OrderSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to create orders")

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if data.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this office")

    try:
        with transaction.atomic():
            office = Office.objects.get(id=data.office_id)
            assigned_to = None
            if data.assigned_to_id:
                assigned_to = User.objects.get(id=data.assigned_to_id)
            customer_company = None
            if data.customer_company_id:
                customer_company = Company.objects.get(id=data.customer_company_id)
            elif data.company_code and data.company_code.strip():
                # Search for company by code/name and create if not found
                company_search_term = data.company_code.strip()

                # First try exact code match
                try:
                    customer_company = Company.objects.get(
                        code__iexact=company_search_term, office=office
                    )
                except Company.DoesNotExist:
                    # Then try exact name match
                    try:
                        customer_company = Company.objects.get(
                            name__iexact=company_search_term, office=office
                        )
                    except Company.DoesNotExist:
                        # Finally try partial name match (case-insensitive)
                        companies = Company.objects.filter(
                            Q(name__icontains=company_search_term)
                            | Q(code__icontains=company_search_term),
                            office=office,
                        )
                        if companies.exists():
                            # Use the first match found
                            customer_company = companies.first()
                        else:
                            # No match found, create new company
                            customer_company = Company.objects.create(
                                office=office,
                                code=company_search_term,
                                name=company_search_term,
                                address="",
                                phone="",
                                color_code="#3B82F6",  # Default blue color
                            )
            status_template = None
            if data.status_template_id:
                status_template = OrderStatusTemplate.objects.get(
                    id=data.status_template_id
                )

            orders_sheet = None
            if data.orders_sheet_id:
                try:
                    orders_sheet = OrdersSheet.objects.get(id=data.orders_sheet_id)
                    # Check if user has access to this sheet
                    if request.user.role == Role.MANAGER:
                        user_offices = request.user.offices.values_list(
                            "office_id", flat=True
                        )
                        if orders_sheet.office_id not in user_offices:
                            raise HttpException(
                                403, "You don't have access to this sheet"
                            )
                except OrdersSheet.DoesNotExist:
                    raise HttpException(404, "Sheet not found")

            # Set completed_at if status is completed or cancelled
            completed_at = None
            if data.handling_status in [
                OrderHandlingStatus.DELIVERED,
                OrderHandlingStatus.CANCELLED,
            ]:
                completed_at = timezone.now()

            order = Order.objects.create(
                office=office,
                orders_sheet=orders_sheet,
                notes=data.notes,
                total_price=data.total_price,
                customer_name=data.customer_name,
                customer_phone=data.customer_phone,
                customer_address=data.customer_address,
                customer_company=customer_company,
                breakable=data.breakable,
                deadline_date=data.deadline_date,
                commission_fixed_rate=data.commission_fixed_rate,
                assigned_to=assigned_to,
                assigned_at=timezone.now() if assigned_to else None,
                final_customer_payment=data.final_customer_payment,
                handling_status=data.handling_status,
                completed_at=completed_at,
                status_template=status_template,
                status_reason=data.status_reason,
            )

            # Create initial status history
            OrderStatusHistory.objects.create(
                order=order,
                status=data.handling_status,
                notes="Order created",
                created_by=request.user,
            )

            # Create assignee history if assigned
            if assigned_to:
                OrderAssigneeHistory.objects.create(
                    order=order,
                    assignee=assigned_to,
                    assigned_by=request.user,
                )

            # Create order schema with company and sheet details
            order_data = OrderSchema.from_orm(order)
            if order.customer_company:
                order_data.customer_company = CompanySchema.from_orm(
                    order.customer_company
                )
            if order.orders_sheet:
                from ..schemas import OrdersSheetSchema

                order_data.orders_sheet = OrdersSheetSchema.from_orm(order.orders_sheet)
            return order_data
    except Exception as e:
        raise HttpException(400, str(e))


# POST /api/orders/bulk
@router.post("/bulk", auth=AuthKey, response=BulkCreateOrderResponseSchema)
def create_orders_bulk(
    request: HttpRequest, data: List[CreateOrderSchema]
) -> BulkCreateOrderResponseSchema:
    """
    Create multiple orders in bulk with transaction support and comprehensive error handling.

    This endpoint processes multiple orders atomically - either all orders are created successfully
    or none are created if any validation fails. Each order is validated individually and
    detailed error information is provided for failed orders.
    """
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to create orders")

    if not data:
        raise HttpException(400, "No orders provided for bulk creation")

    if len(data) > 100:  # Limit bulk creation to 100 orders at once
        raise HttpException(400, "Cannot create more than 100 orders at once")

    # Validate office access for managers
    user_offices = None
    user_offices = set(request.user.offices.values_list("office_id", flat=True))

    created_orders = []
    failed_orders = []
    errors = []

    # Pre-validate all orders before attempting creation
    validation_errors = []
    for idx, order_data in enumerate(data):
        try:
            # Validate office access for managers
            if order_data.office_id not in user_offices:
                validation_errors.append(
                    {
                        "index": idx,
                        "error": f"You don't have access to office {order_data.office_id}",
                        "office_id": order_data.office_id,
                    }
                )

            # Validate required fields
            if not order_data.customer_name or not order_data.customer_name.strip():
                validation_errors.append(
                    {
                        "index": idx,
                        "error": "Customer name is required",
                        "field": "customer_name",
                    }
                )

            if not order_data.customer_phone or not order_data.customer_phone.strip():
                validation_errors.append(
                    {
                        "index": idx,
                        "error": "Customer phone is required",
                        "field": "customer_phone",
                    }
                )

        except Exception as e:
            error_msg = f"Validation error: {str(e)}"
            validation_errors.append(
                {"index": idx, "error": error_msg, "details": str(e)}
            )

    if validation_errors:
        return BulkCreateOrderResponseSchema(
            success=False,
            created_orders=[],
            failed_orders=[
                {"index": error["index"], "error": error["error"], "details": error}
                for error in validation_errors
            ],
            total_processed=len(data),
            successful_count=0,
            failed_count=len(validation_errors),
            errors=[error["error"] for error in validation_errors],
        )

    # Process orders in a single transaction
    try:
        with transaction.atomic():
            # Cache frequently used objects to avoid duplicate queries
            office_cache: dict[str, Office] = {}
            user_cache: dict[str, User] = {}
            company_cache: dict[str, Company] = {}
            template_cache: dict[str, OrderStatusTemplate] = {}
            sheet_cache: dict[str, OrdersSheet] = {}

            for idx, order_data in enumerate(data):
                try:
                    # Get or cache office
                    if order_data.office_id not in office_cache:
                        office_cache[order_data.office_id] = Office.objects.get(
                            id=order_data.office_id
                        )
                    office = office_cache[order_data.office_id]

                    # Get or cache assigned user
                    assigned_to = None
                    if order_data.assigned_to_id:
                        if order_data.assigned_to_id not in user_cache:
                            user_cache[order_data.assigned_to_id] = User.objects.get(
                                id=order_data.assigned_to_id
                            )
                        assigned_to = user_cache[order_data.assigned_to_id]

                    # Get or cache customer company
                    customer_company = None
                    if order_data.customer_company_id:
                        if order_data.customer_company_id not in company_cache:
                            company_cache[order_data.customer_company_id] = (
                                Company.objects.get(id=order_data.customer_company_id)
                            )
                        customer_company = company_cache[order_data.customer_company_id]
                    elif order_data.company_code and order_data.company_code.strip():
                        # Search for company by code/name and create if not found
                        company_search_term = order_data.company_code.strip()
                        code_key = f"{office.id}_{company_search_term}"

                        if code_key not in company_cache:
                            # First try exact code match
                            try:
                                company_cache[code_key] = Company.objects.get(
                                    code__iexact=company_search_term, office=office
                                )
                            except Company.DoesNotExist:
                                # Then try exact name match
                                try:
                                    company_cache[code_key] = Company.objects.get(
                                        name__iexact=company_search_term, office=office
                                    )
                                except Company.DoesNotExist:
                                    # Finally try partial name match (case-insensitive)
                                    companies = Company.objects.filter(
                                        Q(name__icontains=company_search_term)
                                        | Q(code__icontains=company_search_term),
                                        office=office,
                                    )
                                    if companies.exists():
                                        # Use the first match found
                                        company_cache[code_key] = companies.first()
                                    else:
                                        # No match found, create new company
                                        company_cache[code_key] = (
                                            Company.objects.create(
                                                office=office,
                                                code=company_search_term,
                                                name=company_search_term,
                                                address="",
                                                phone="",
                                                color_code="#3B82F6",  # Default blue color
                                            )
                                        )
                        customer_company = company_cache[code_key]

                    # Get or cache status template
                    status_template = None
                    if order_data.status_template_id:
                        if order_data.status_template_id not in template_cache:
                            template_cache[order_data.status_template_id] = (
                                OrderStatusTemplate.objects.get(
                                    id=order_data.status_template_id
                                )
                            )
                        status_template = template_cache[order_data.status_template_id]

                    # Get or cache orders sheet
                    orders_sheet = None
                    if order_data.orders_sheet_id:
                        if order_data.orders_sheet_id not in sheet_cache:
                            sheet_cache[order_data.orders_sheet_id] = (
                                OrdersSheet.objects.get(id=order_data.orders_sheet_id)
                            )
                        orders_sheet = sheet_cache[order_data.orders_sheet_id]
                        # Check if user has access to this sheet
                        if request.user.role == Role.MANAGER:
                            if orders_sheet.office_id not in user_offices:
                                raise HttpException(
                                    403, "You don't have access to this sheet"
                                )

                    # Set completed_at if status is completed or cancelled
                    completed_at = None
                    if order_data.handling_status in [
                        OrderHandlingStatus.DELIVERED,
                        OrderHandlingStatus.CANCELLED,
                    ]:
                        completed_at = timezone.now()

                    # Create order
                    order = Order.objects.create(
                        office=office,
                        orders_sheet=orders_sheet,
                        notes=order_data.notes,
                        total_price=order_data.total_price,
                        customer_name=order_data.customer_name,
                        customer_phone=order_data.customer_phone,
                        customer_address=order_data.customer_address,
                        customer_company=customer_company,
                        breakable=order_data.breakable,
                        deadline_date=order_data.deadline_date,
                        commission_fixed_rate=order_data.commission_fixed_rate,
                        assigned_to=assigned_to,
                        assigned_at=timezone.now() if assigned_to else None,
                        final_customer_payment=order_data.final_customer_payment,
                        handling_status=order_data.handling_status,
                        completed_at=completed_at,
                        status_template=status_template,
                        status_reason=order_data.status_reason,
                    )

                    # Create initial status history
                    OrderStatusHistory.objects.create(
                        order=order,
                        status=order_data.handling_status,
                        notes="Order created",
                        created_by=request.user,
                    )

                    # Create assignee history if assigned
                    if assigned_to:
                        OrderAssigneeHistory.objects.create(
                            order=order,
                            assignee=assigned_to,
                            assigned_by=request.user,
                        )

                    created_orders.append(order)

                except Office.DoesNotExist:
                    error_msg = f"Office with id {order_data.office_id} not found"
                    failed_orders.append(
                        {
                            "index": idx,
                            "error": error_msg,
                            "office_id": order_data.office_id,
                        }
                    )
                    errors.append(error_msg)

                except User.DoesNotExist:
                    error_msg = f"User with id {order_data.assigned_to_id} not found"
                    failed_orders.append(
                        {
                            "index": idx,
                            "error": error_msg,
                            "assigned_to_id": order_data.assigned_to_id,
                        }
                    )
                    errors.append(error_msg)

                except Company.DoesNotExist:
                    error_msg = (
                        f"Company with id {order_data.customer_company_id} not found"
                    )
                    failed_orders.append(
                        {
                            "index": idx,
                            "error": error_msg,
                            "customer_company_id": order_data.customer_company_id,
                        }
                    )
                    errors.append(error_msg)

                except OrderStatusTemplate.DoesNotExist:
                    error_msg = f"Status template with id {order_data.status_template_id} not found"
                    failed_orders.append(
                        {
                            "index": idx,
                            "error": error_msg,
                            "status_template_id": order_data.status_template_id,
                        }
                    )
                    errors.append(error_msg)

                except Exception as e:
                    error_msg = f"Error creating order: {str(e)}"
                    failed_orders.append(
                        {"index": idx, "error": error_msg, "details": str(e)}
                    )
                    errors.append(error_msg)

            # If any orders failed, roll back the entire transaction
            if failed_orders:
                raise Exception("Some orders failed validation or creation")

            # Convert orders to schema with company details
            order_schemas = []
            for order in created_orders:
                order_data = OrderSchema.from_orm(order)
                if order.customer_company:
                    order_data.customer_company = CompanySchema.from_orm(
                        order.customer_company
                    )
                order_schemas.append(order_data)

            return BulkCreateOrderResponseSchema(
                success=True,
                created_orders=order_schemas,
                failed_orders=[],
                total_processed=len(data),
                successful_count=len(created_orders),
                failed_count=0,
                errors=[],
            )

    except Exception as e:
        # Transaction will be automatically rolled back
        error_msg = str(e)
        if not failed_orders:
            # If we reach here without specific failed orders, it's a system error
            failed_orders.append(
                {
                    "index": -1,
                    "error": f"System error: {error_msg}",
                    "details": error_msg,
                }
            )
            errors.append(f"System error: {error_msg}")

        return BulkCreateOrderResponseSchema(
            success=False,
            created_orders=[],
            failed_orders=failed_orders,
            total_processed=len(data),
            successful_count=0,
            failed_count=len(failed_orders),
            errors=errors,
        )


# GET /api/orders/{order_id}
@router.get("/{order_id}", auth=AuthKey)
def get_order(request: HttpRequest, order_id: str) -> OrderSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpException(403, "You don't have permission to view orders")

    # Check if user has access to this order
    if request.user.role in [Role.MANAGER, Role.EMPLOYEE]:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        order = Order.objects.get(id=order_id)
        if order.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this order")

    order = Order.objects.get(id=order_id)
    order_data = OrderSchema.from_orm(order)
    if order.customer_company:
        order_data.customer_company = CompanySchema.from_orm(order.customer_company)
    if order.orders_sheet:
        from ..schemas import OrdersSheetSchema

        order_data.orders_sheet = OrdersSheetSchema.from_orm(order.orders_sheet)
    return order_data


# PUT /api/orders/{order_id}
@router.put("/{order_id}", auth=AuthKey)
def update_order(
    request: HttpRequest, order_id: str, data: UpdateOrderSchema
) -> OrderSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpException(403, "You don't have permission to update orders")

    # Check if user has access to this order
    if request.user.role in [Role.MANAGER, Role.EMPLOYEE]:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        order = Order.objects.get(id=order_id)
        if order.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this order")

    try:
        with transaction.atomic():
            order = Order.objects.get(id=order_id)
            old_status = order.handling_status
            old_assigned_to = order.assigned_to

            # Handle orders sheet
            if data.orders_sheet_id:
                orders_sheet = OrdersSheet.objects.get(id=data.orders_sheet_id)
                # Check if user has access to this sheet
                if request.user.role == Role.MANAGER:
                    user_offices = request.user.offices.values_list(
                        "office_id", flat=True
                    )
                    if orders_sheet.office_id not in user_offices:
                        raise HttpException(403, "You don't have access to this sheet")
                order.orders_sheet = orders_sheet
            else:
                order.orders_sheet = None

            # Update order fields
            order.notes = data.notes
            order.total_price = data.total_price
            order.customer_name = data.customer_name
            order.customer_phone = data.customer_phone
            order.customer_address = data.customer_address
            order.breakable = data.breakable
            order.deadline_date = data.deadline_date
            order.commission_fixed_rate = data.commission_fixed_rate
            order.final_customer_payment = data.final_customer_payment
            order.handling_status = data.handling_status
            order.status_reason = data.status_reason

            # Set completed_at if status is completed or cancelled
            if data.handling_status in [
                OrderHandlingStatus.DELIVERED,
                OrderHandlingStatus.CANCELLED,
            ]:
                if not order.completed_at:  # Only set if not already set
                    order.completed_at = timezone.now()
            else:
                # Clear completed_at if status is changed back to pending/processing
                order.completed_at = None

            # Handle customer company
            if data.customer_company_id:
                order.customer_company = Company.objects.get(
                    id=data.customer_company_id
                )
            else:
                order.customer_company = None

            # Handle status template
            if data.status_template_id:
                order.status_template = OrderStatusTemplate.objects.get(
                    id=data.status_template_id
                )
                order.status_reason = order.status_template.name
                data.handling_status = (
                    order.status_template.order_default_handling_status
                )
                if order.status_template.just_delivery_commission_rate:
                    order.final_customer_payment = float(
                        order.commission_fixed_rate or 0
                    )
                else:
                    order.final_customer_payment = (
                        float(order.total_price or 0)
                        * (
                            float(order.status_template.percentage_of_order_total_price)
                            / 100
                        )
                    ) + float(order.status_template.commission_fixed_rate or 0)
            else:
                order.status_template = None

            # Handle assignment
            if data.assigned_to_id:
                if not order.assigned_to:
                    order.handling_status = OrderHandlingStatus.ASSIGNED
                new_assigned_to = User.objects.get(id=data.assigned_to_id)
                order.assigned_to = new_assigned_to
                order.assigned_at = timezone.now()
            else:
                if order.assigned_to:
                    order.handling_status = OrderHandlingStatus.PENDING
                order.assigned_to = None
                order.assigned_at = None

            order.save()

            # Create status history if status changed
            if old_status != data.handling_status:
                OrderStatusHistory.objects.create(
                    order=order,
                    status=data.handling_status,
                    notes=data.status_reason
                    if data.handling_status == OrderHandlingStatus.CANCELLED
                    else "Status updated",
                    created_by=request.user,
                )

            # Create assignee history if assignment changed
            if old_assigned_to != order.assigned_to:
                if order.assigned_to:
                    OrderAssigneeHistory.objects.create(
                        order=order,
                        assignee=order.assigned_to,
                        assigned_by=request.user,
                    )

            # Create order schema with company and sheet details
            order_data = OrderSchema.from_orm(order)
            if order.customer_company:
                order_data.customer_company = CompanySchema.from_orm(
                    order.customer_company
                )
            if order.orders_sheet:
                from ..schemas import OrdersSheetSchema

                order_data.orders_sheet = OrdersSheetSchema.from_orm(order.orders_sheet)
            return order_data
    except Exception as e:
        raise HttpException(400, str(e))


# DELETE /api/orders/{order_id}
@router.delete("/{order_id}", auth=AuthKey)
def delete_order(request: HttpRequest, order_id: str):
    if request.user.role != Role.ADMIN:
        raise HttpException(403, "You don't have permission to delete orders")

    order = Order.objects.get(id=order_id)
    order.delete()
    return "OK"
