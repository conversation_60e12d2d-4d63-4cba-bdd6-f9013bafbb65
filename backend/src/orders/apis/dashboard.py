from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional

from django.db.models import Sum, Count, Q
from django.http import HttpRequest
from django.utils import timezone

from accounts.models import Role, User
from merchants.models import Office

from ..models import Order, OrdersSheet, OrderHandlingStatus
from ..schemas import (
    DashboardStatsSchema,
    DashboardEmployeesSchema,
    EmployeeStatsSchema,
    DashboardChartsSchema,
    ChartDataPointSchema,
)
from core.middleware import Auth<PERSON><PERSON>
from core.utils import HttpException

from . import router


# Dashboard Stats endpoint
# GET /api/orders/dashboard/stats/
@router.get("/dashboard/stats/", auth=AuthKey, response=DashboardStatsSchema)
def get_dashboard_stats(
    request: HttpRequest,
    sheet_id: Optional[str] = None,
    office_id: Optional[str] = None,
):
    """Get dashboard statistics with optional sheet filtering"""
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to view dashboard stats")

    # Build base queryset based on user role
    if request.user.role == Role.ADMIN:
        orders = Order.objects.all()
    else:
        # Managers can only see orders in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        orders = Order.objects.filter(office_id__in=user_offices)

    # Apply filters
    if office_id:
        orders = orders.filter(office_id=office_id)

    if sheet_id:
        orders = orders.filter(orders_sheet_id=sheet_id)

    # Calculate stats
    stats = orders.aggregate(
        total_orders=Count("id"),
        total_amount=Sum("total_price"),
        collected_cash=Sum("final_customer_payment"),
        total_commission=Sum("commission_fixed_rate"),
    )

    return DashboardStatsSchema(
        total_orders=stats["total_orders"],
        total_amount=float(stats["total_amount"] or 0),
        collected_cash=float(stats["collected_cash"] or 0),
        total_commission=float(stats["total_commission"] or 0),
    )


# Dashboard Employees endpoint
# GET /api/orders/dashboard/employees/
@router.get("/dashboard/employees/", auth=AuthKey, response=DashboardEmployeesSchema)
def get_dashboard_employees(
    request: HttpRequest,
    sheet_id: Optional[str] = None,
    office_id: Optional[str] = None,
):
    """Get employee performance metrics with optional sheet filtering"""
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to view employee stats")

    # Build base queryset based on user role
    if request.user.role == Role.ADMIN:
        # Get all employees
        employees = User.objects.filter(role=Role.EMPLOYEE)
        orders_base = Order.objects.all()
    else:
        # Managers can only see employees in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        employees = User.objects.filter(
            role=Role.EMPLOYEE, offices__office_id__in=user_offices
        ).distinct()
        orders_base = Order.objects.filter(office_id__in=user_offices)

    # Apply office filter if provided
    if office_id:
        employees = employees.filter(offices__office_id=office_id).distinct()
        orders_base = orders_base.filter(office_id=office_id)

    # Apply sheet filter if provided
    if sheet_id:
        orders_base = orders_base.filter(orders_sheet_id=sheet_id)

    employee_stats = []
    for employee in employees:
        # Get orders for this employee
        employee_orders = orders_base.filter(assigned_to=employee)

        # Calculate stats
        stats = employee_orders.aggregate(
            orders_count=Count("id"),
            completed_orders_count=Count("id", filter=Q(completed_at__isnull=False)),
            delivered_orders_count=Count(
                "id", filter=Q(handling_status=OrderHandlingStatus.DELIVERED)
            ),
            cancelled_orders_count=Count(
                "id", filter=Q(handling_status=OrderHandlingStatus.CANCELLED)
            ),
            total_customer_payments=Sum("final_customer_payment"),
        )

        employee_stats.append(
            EmployeeStatsSchema(
                id=employee.id,
                name=f"{employee.first_name} {employee.last_name}".strip()
                or employee.username,
                orders_count=stats["orders_count"],
                completed_orders_count=stats["completed_orders_count"],
                delivered_orders_count=stats["delivered_orders_count"],
                cancelled_orders_count=stats["cancelled_orders_count"],
                current_wallet_balance=float(employee.wallet.balance),
                total_customer_payments=float(stats["total_customer_payments"] or 0),
            )
        )

    return DashboardEmployeesSchema(employees=employee_stats)


# Dashboard Charts endpoint (main tab only)
# GET /api/orders/dashboard/charts/
@router.get("/dashboard/charts/", auth=AuthKey, response=DashboardChartsSchema)
def get_dashboard_charts(request: HttpRequest, office_id: Optional[str] = None):
    """Get revenue data grouped by sheet across different time periods (main tab only)"""
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to view dashboard charts")

    # Build base queryset based on user role
    if request.user.role == Role.ADMIN:
        sheets = OrdersSheet.objects.filter(active=True)
        orders_base = Order.objects.all()
    else:
        # Managers can only see sheets in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        sheets = OrdersSheet.objects.filter(office_id__in=user_offices, active=True)
        orders_base = Order.objects.filter(office_id__in=user_offices)

    # Apply office filter if provided
    if office_id:
        sheets = sheets.filter(office_id=office_id)
        orders_base = orders_base.filter(office_id=office_id)

    # Define time periods
    now = timezone.now()
    periods = {
        "7_days": now - timedelta(days=7),
        "1_month": now - timedelta(days=30),
        "3_months": now - timedelta(days=90),
    }

    chart_data = []

    # Add data for each sheet and time period
    for sheet in sheets:
        for period_name, start_date in periods.items():
            sheet_orders = orders_base.filter(
                orders_sheet=sheet,
                created_at__gte=start_date,
                handling_status__in=[
                    OrderHandlingStatus.DELIVERED,
                    OrderHandlingStatus.CANCELLED,
                ],
            )

            revenue = sheet_orders.aggregate(
                total_revenue=Sum("final_customer_payment")
            )["total_revenue"]

            chart_data.append(
                ChartDataPointSchema(
                    sheet_id=sheet.id,
                    sheet_name=sheet.name,
                    revenue=float(revenue or 0),
                    period=period_name,
                )
            )

    # Add aggregated data for "All Sheets"
    for period_name, start_date in periods.items():
        all_orders = orders_base.filter(
            created_at__gte=start_date,
            handling_status__in=[
                OrderHandlingStatus.DELIVERED,
                OrderHandlingStatus.CANCELLED,
            ],
        )

        revenue = all_orders.aggregate(total_revenue=Sum("final_customer_payment"))[
            "total_revenue"
        ]

        chart_data.append(
            ChartDataPointSchema(
                sheet_id=None,
                sheet_name="All Sheets",
                revenue=float(revenue or 0),
                period=period_name,
            )
        )

    return DashboardChartsSchema(chart_data=chart_data)
