from django.core.paginator import Paginator
from django.http import HttpRequest

from accounts.models import Role

from ..models import Order, OrderAssigneeHistory
from ..schemas import (
    OrderAssigneeHistorySchema,
    OrderAssigneeHistoryListResponseSchema,
)
from core.middleware import <PERSON><PERSON><PERSON><PERSON>

from . import router


# Order Assignee History endpoints
# GET /api/orders/{order_id}/assignee-history/
@router.get(
    "/{order_id}/assignee-history/",
    auth=AuthKey,
    response=OrderAssigneeHistoryListResponseSchema,
)
def list_order_assignee_history(
    request: HttpRequest,
    order_id: str,
    page: int = 1,
    page_size: int = 10,
):
    if request.user.role not in [Role.ADMIN, Role.MANAGER, Role.EMPLOYEE]:
        return 403, {"message": "You don't have permission to view assignee history"}

    # Check if user has access to this order
    if request.user.role in [Role.MANAGER, Role.EMPLOYEE]:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        order = Order.objects.get(id=order_id)
        if order.office_id not in user_offices:
            return 403, {"message": "You don't have access to this order"}

    history = OrderAssigneeHistory.objects.filter(order_id=order_id).order_by(
        "-created_at"
    )

    paginator = Paginator(history, page_size)
    page_obj = paginator.get_page(page)

    return OrderAssigneeHistoryListResponseSchema(
        history=[OrderAssigneeHistorySchema.from_orm(item) for item in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )
