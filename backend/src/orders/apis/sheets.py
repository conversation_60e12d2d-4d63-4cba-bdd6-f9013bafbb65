from typing import List

from django.core.paginator import Paginator
from django.http import HttpRequest

from accounts.models import Role
from merchants.models import Office

from ..models import OrdersSheet
from ..schemas import (
    OrdersSheetSchema,
    CreateOrdersSheetSchema,
    UpdateOrdersSheetSchema,
    OrdersSheetListResponseSchema,
)
from core.middleware import <PERSON>th<PERSON><PERSON>
from core.utils import HttpException

from . import router


# Sheet endpoints
# GET /api/orders/sheets/
@router.get("/sheets/", auth=AuthKey, response=OrdersSheetListResponseSchema)
def list_sheets(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    office_id: str | None = None,
    active_only: bool = False,
):
    # Check if user has permission to list sheets
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to list sheets")

    # Build queryset based on user role
    if request.user.role == Role.ADMIN:
        sheets = OrdersSheet.objects.all()
    else:
        # Managers can only see sheets in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        sheets = OrdersSheet.objects.filter(office_id__in=user_offices)

    # Apply filters
    if office_id:
        sheets = sheets.filter(office_id=office_id)
    
    if active_only:
        sheets = sheets.filter(active=True)

    sheets = sheets.order_by("-created_at")

    # Paginate results
    paginator = Paginator(sheets, page_size)
    page_obj = paginator.get_page(page)

    return OrdersSheetListResponseSchema(
        count=paginator.count,
        next=f"?page={page_obj.next_page_number()}" if page_obj.has_next() else None,
        previous=f"?page={page_obj.previous_page_number()}" if page_obj.has_previous() else None,
        results=[OrdersSheetSchema.from_orm(sheet) for sheet in page_obj],
    )


# GET /api/orders/sheets/active/
@router.get("/sheets/active/", auth=AuthKey, response=List[OrdersSheetSchema])
def list_active_sheets(request: HttpRequest, office_id: str | None = None):
    """Get active sheets for dropdown selection"""
    # Check if user has permission to list sheets
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to list sheets")

    # Build queryset based on user role
    if request.user.role == Role.ADMIN:
        sheets = OrdersSheet.objects.filter(active=True)
    else:
        # Managers can only see sheets in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        sheets = OrdersSheet.objects.filter(office_id__in=user_offices, active=True)

    # Apply office filter if provided
    if office_id:
        sheets = sheets.filter(office_id=office_id)

    sheets = sheets.order_by("name")

    return [OrdersSheetSchema.from_orm(sheet) for sheet in sheets]


# POST /api/orders/sheets/
@router.post("/sheets/", auth=AuthKey, response=OrdersSheetSchema)
def create_sheet(request: HttpRequest, data: CreateOrdersSheetSchema) -> OrdersSheetSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to create sheets")

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if data.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this office")

    # Validate office exists
    try:
        office = Office.objects.get(id=data.office_id)
    except Office.DoesNotExist:
        raise HttpException(404, "Office not found")

    # Create the sheet
    sheet = OrdersSheet.objects.create(
        office=office,
        name=data.name,
        active=data.active,
        notes=data.notes,
        created_by=request.user,
    )

    return OrdersSheetSchema.from_orm(sheet)


# GET /api/orders/sheets/{sheet_id}
@router.get("/sheets/{sheet_id}", auth=AuthKey, response=OrdersSheetSchema)
def get_sheet(request: HttpRequest, sheet_id: str) -> OrdersSheetSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to view sheets")

    try:
        sheet = OrdersSheet.objects.get(id=sheet_id)
    except OrdersSheet.DoesNotExist:
        raise HttpException(404, "Sheet not found")

    # Check if user has access to this sheet
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if sheet.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this sheet")

    return OrdersSheetSchema.from_orm(sheet)


# PUT /api/orders/sheets/{sheet_id}
@router.put("/sheets/{sheet_id}", auth=AuthKey, response=OrdersSheetSchema)
def update_sheet(
    request: HttpRequest, sheet_id: str, data: UpdateOrdersSheetSchema
) -> OrdersSheetSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to update sheets")

    try:
        sheet = OrdersSheet.objects.get(id=sheet_id)
    except OrdersSheet.DoesNotExist:
        raise HttpException(404, "Sheet not found")

    # Check if user has access to this sheet
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if sheet.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this sheet")

    # Update the sheet
    sheet.name = data.name
    sheet.active = data.active
    sheet.notes = data.notes
    sheet.save()

    return OrdersSheetSchema.from_orm(sheet)


# DELETE /api/orders/sheets/{sheet_id}
@router.delete("/sheets/{sheet_id}", auth=AuthKey)
def delete_sheet(request: HttpRequest, sheet_id: str):
    if request.user.role != Role.ADMIN:
        raise HttpException(403, "You don't have permission to delete sheets")

    try:
        sheet = OrdersSheet.objects.get(id=sheet_id)
    except OrdersSheet.DoesNotExist:
        raise HttpException(404, "Sheet not found")

    sheet.delete()
    return "OK"
