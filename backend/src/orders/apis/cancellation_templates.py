from django.core.paginator import Paginator
from django.db.models import Q
from django.http import HttpRequest

from accounts.models import Role
from merchants.models import Office

from ..models import OrderStatusTemplate
from ..schemas import (
    OrderStatusTemplateSchema,
    CreateOrderStatusTemplateSchema,
    UpdateOrderStatusTemplateSchema,
    OrderStatusTemplateListResponseSchema,
)
from core.middleware import AuthKey
from core.utils import HttpException

from . import router


# Order Cancellation Reason Template endpoints
# GET /api/orders/cancellation-templates/
@router.get(
    "/cancellation-templates/",
    auth=AuthKey,
    response=OrderStatusTemplateListResponseSchema,
)
def list_cancellation_templates(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    office_id: str | None = None,
):
    # Check if user has permission to list cancellation templates
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(
            403, "You don't have permission to list cancellation templates"
        )

    user_offices = request.user.offices.values_list("office_id", flat=True)
    if office_id:
        user_offices = [office_id]

    templates = OrderStatusTemplate.objects.filter(
        Q(office_id__in=user_offices) | Q(office_id__isnull=True)
    )

    templates = templates.order_by("name")

    paginator = Paginator(templates, page_size)
    page_obj = paginator.get_page(page)

    return OrderStatusTemplateListResponseSchema(
        templates=[
            OrderStatusTemplateSchema.from_orm(template) for template in page_obj
        ],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/orders/cancellation-templates/
@router.post(
    "/cancellation-templates/",
    auth=AuthKey,
    response=OrderStatusTemplateSchema,
)
def create_cancellation_template(
    request: HttpRequest, data: CreateOrderStatusTemplateSchema
) -> OrderStatusTemplateSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(
            403, "You don't have permission to create cancellation templates"
        )

    # Check if user has access to this office
    if data.office_id and request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if data.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this office")

    try:
        office = None
        if data.office_id:
            office = Office.objects.get(id=data.office_id)

        template = OrderStatusTemplate.objects.create(
            office=office,
            name=data.name,
            reason_template_text=data.reason_template_text,
            order_default_handling_status=data.order_default_handling_status,
            just_delivery_commission_rate=data.just_delivery_commission_rate,
            commission_fixed_rate=data.commission_fixed_rate,
            percentage_of_order_total_price=data.percentage_of_order_total_price,
        )
        return OrderStatusTemplateSchema.from_orm(template)
    except Exception as e:
        raise HttpException(400, str(e))


# GET /api/orders/cancellation-templates/{template_id}
@router.get(
    "/cancellation-templates/{template_id}",
    auth=AuthKey,
    response=OrderStatusTemplateSchema,
)
def get_cancellation_template(
    request: HttpRequest, template_id: str
) -> OrderStatusTemplateSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {
            "message": "You don't have permission to view cancellation templates"
        }

    # Check if user has access to this template
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        template = OrderStatusTemplate.objects.get(id=template_id)
        if template.office_id and template.office_id not in user_offices:
            return 403, {"message": "You don't have access to this template"}

    template = OrderStatusTemplate.objects.get(id=template_id)
    return OrderStatusTemplateSchema.from_orm(template)


# PUT /api/orders/cancellation-templates/{template_id}
@router.put(
    "/cancellation-templates/{template_id}",
    auth=AuthKey,
    response=OrderStatusTemplateSchema,
)
def update_cancellation_template(
    request: HttpRequest,
    template_id: str,
    data: UpdateOrderStatusTemplateSchema,
) -> OrderStatusTemplateSchema:
    if request.user.role not in [Role.ADMIN]:
        raise HttpException(
            403, "You don't have permission to update cancellation templates"
        )

    # Check if user has access to this template
    user_offices = request.user.offices.values_list("office_id", flat=True)
    template = OrderStatusTemplate.objects.get(id=template_id)
    if template.office and template.office.id not in user_offices:
        raise HttpException(403, "You don't have access to this template")

    if not template.office:
        raise HttpException(403, "You don't have access to this template")

    template = OrderStatusTemplate.objects.get(id=template_id)
    template.name = data.name
    template.reason_template_text = data.reason_template_text
    template.order_default_handling_status = data.order_default_handling_status
    template.just_delivery_commission_rate = data.just_delivery_commission_rate
    template.commission_fixed_rate = data.commission_fixed_rate
    template.percentage_of_order_total_price = data.percentage_of_order_total_price
    template.save()

    return OrderStatusTemplateSchema.from_orm(template)


# DELETE /api/orders/cancellation-templates/{template_id}
@router.delete("/cancellation-templates/{template_id}", auth=AuthKey, response=str)
def delete_cancellation_template(request: HttpRequest, template_id: str):
    if request.user.role not in [Role.ADMIN]:
        raise HttpException(
            403, "You don't have permission to delete cancellation templates"
        )

        # Check if user has access to this template
    user_offices = request.user.offices.values_list("office_id", flat=True)
    template = OrderStatusTemplate.objects.get(id=template_id)
    if template.office_id and template.office_id not in user_offices:
        raise HttpException(403, "You don't have access to this template")

    if not template.office:
        raise HttpException(403, "You don't have access to this template")

    template = OrderStatusTemplate.objects.get(id=template_id)
    template.delete()
    return "OK"
