from ninja import Router

# Shared router for all orders endpoints
router = Router(tags=["orders"])

# Import submodules to register their routes on the shared router
from . import companies  # noqa: F401
from . import company_channels  # noqa: F401
from . import cancellation_templates  # noqa: F401
from . import orders  # noqa: F401
from . import sheets  # noqa: F401
from . import dashboard  # noqa: F401
from . import status_history  # noqa: F401
from . import assignee_history  # noqa: F401

__all__ = ["router"]
