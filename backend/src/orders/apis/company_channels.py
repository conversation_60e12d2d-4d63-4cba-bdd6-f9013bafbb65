from django.core.paginator import Paginator
from django.http import HttpRequest

from accounts.models import Role
from merchants.models import Office, Merchant

from ..models import Company, CompanyChannel
from ..schemas import (
    CompanyChannelSchema,
    CreateCompanyChannelSchema,
    UpdateCompanyChannelSchema,
    CompanyChannelListResponseSchema,
)
from core.middleware import <PERSON>th<PERSON><PERSON>
from core.utils import HttpException

from . import router


# Company Channel endpoints
# GET /api/orders/company-channels/
@router.get(
    "/company-channels/", auth=AuthKey, response=CompanyChannelListResponseSchema
)
def list_company_channels(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    merchant_id: str | None = None,
    office_id: str | None = None,
    company_id: str | None = None,
):
    # Check if user has permission to list company channels
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to list company channels")

    # Build queryset based on user role
    if request.user.role == Role.ADMIN:
        channels = CompanyChannel.objects.all()
    else:
        # Managers can only see channels in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        channels = CompanyChannel.objects.filter(office_id__in=user_offices)

    # Apply filters
    if merchant_id:
        channels = channels.filter(merchant_id=merchant_id)
    if office_id:
        channels = channels.filter(office_id=office_id)
    if company_id:
        channels = channels.filter(company_id=company_id)

    channels = channels.order_by("name")

    paginator = Paginator(channels, page_size)
    page_obj = paginator.get_page(page)

    return CompanyChannelListResponseSchema(
        channels=[CompanyChannelSchema.from_orm(channel) for channel in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/orders/company-channels/
@router.post("/company-channels/", auth=AuthKey, response=CompanyChannelSchema)
def create_company_channel(
    request: HttpRequest, data: CreateCompanyChannelSchema
) -> CompanyChannelSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to create company channels")

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if data.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this office")

    try:
        merchant = Merchant.objects.get(id=data.merchant_id)
        office = Office.objects.get(id=data.office_id)
        company = None
        if data.company_id:
            company = Company.objects.get(id=data.company_id)

        channel = CompanyChannel.objects.create(
            merchant=merchant,
            office=office,
            company=company,
            name=data.name,
            notes=data.notes,
            channel_whatsapp_number=data.channel_whatsapp_number,
        )
        return CompanyChannelSchema.from_orm(channel)
    except Exception as e:
        raise HttpException(400, str(e))


# GET /api/orders/company-channels/{channel_id}
@router.get(
    "/company-channels/{channel_id}", auth=AuthKey, response=CompanyChannelSchema
)
def get_company_channel(request: HttpRequest, channel_id: str) -> CompanyChannelSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to view company channels")

    # Check if user has access to this channel
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        channel = CompanyChannel.objects.get(id=channel_id)
        if channel.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this channel")

    channel = CompanyChannel.objects.get(id=channel_id)
    return CompanyChannelSchema.from_orm(channel)


# PUT /api/orders/company-channels/{channel_id}
@router.put(
    "/company-channels/{channel_id}", auth=AuthKey, response=CompanyChannelSchema
)
def update_company_channel(
    request: HttpRequest, channel_id: str, data: UpdateCompanyChannelSchema
) -> CompanyChannelSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to update company channels")

    # Check if user has access to this channel
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        channel = CompanyChannel.objects.get(id=channel_id)
        if channel.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this channel")

    channel = CompanyChannel.objects.get(id=channel_id)
    if data.company_id:
        company = Company.objects.get(id=data.company_id)
        channel.company = company
    else:
        channel.company = None
    channel.name = data.name
    channel.notes = data.notes
    channel.channel_whatsapp_number = data.channel_whatsapp_number
    channel.save()

    return CompanyChannelSchema.from_orm(channel)


# DELETE /api/orders/company-channels/{channel_id}
@router.delete("/company-channels/{channel_id}", auth=AuthKey, response=str)
def delete_company_channel(request: HttpRequest, channel_id: str):
    if request.user.role != Role.ADMIN:
        raise HttpException(403, "You don't have permission to delete company channels")

    channel = CompanyChannel.objects.get(id=channel_id)
    channel.delete()
    return "OK"
