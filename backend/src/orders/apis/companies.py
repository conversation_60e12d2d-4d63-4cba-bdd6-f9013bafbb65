from django.core.paginator import Paginator
from django.http import HttpRequest

from accounts.models import Role
from merchants.models import Office

from ..models import Company
from ..schemas import (
    CompanySchema,
    CreateCompanySchema,
    UpdateCompanySchema,
    CompanyListResponseSchema,
)
from core.middleware import Auth<PERSON><PERSON>
from core.utils import HttpException

from . import router


# Company endpoints
# GET /api/orders/companies/
@router.get("/companies/", auth=AuthKey, response=CompanyListResponseSchema)
def list_companies(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 1000,
    office_id: str | None = None,
):
    # Check if user has permission to list companies
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to list companies")

    # Build queryset based on user role
    if request.user.role == Role.ADMIN:
        companies = Company.objects.all()
    else:
        # Managers can only see companies in their offices
        user_offices = request.user.offices.values_list("office_id", flat=True)
        companies = Company.objects.filter(office_id__in=user_offices)

    # Apply filters
    if office_id:
        companies = companies.filter(office_id=office_id)

    companies = companies.order_by("name")

    paginator = Paginator(companies, page_size)
    page_obj = paginator.get_page(page)

    return CompanyListResponseSchema(
        companies=[CompanySchema.from_orm(company) for company in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/orders/companies/
@router.post("/companies/", auth=AuthKey, response=CompanySchema)
def create_company(request: HttpRequest, data: CreateCompanySchema) -> CompanySchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to create companies")

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        if data.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this office")

    try:
        office = Office.objects.get(id=data.office_id)
        company = Company.objects.create(
            office=office,
            code=data.code,
            name=data.name,
            address=data.address,
            phone=data.phone,
            color_code=data.color_code,
        )
        return CompanySchema.from_orm(company)
    except Exception as e:
        raise HttpException(400, str(e))


# GET /api/orders/companies/{company_id}
@router.get("/companies/{company_id}", auth=AuthKey, response=CompanySchema)
def get_company(request: HttpRequest, company_id: str) -> CompanySchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to view companies")

    # Check if user has access to this company
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        company = Company.objects.get(id=company_id)
        if company.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this company")

    company = Company.objects.get(id=company_id)
    return CompanySchema.from_orm(company)


# PUT /api/orders/companies/{company_id}
@router.put("/companies/{company_id}", auth=AuthKey, response=CompanySchema)
def update_company(
    request: HttpRequest, company_id: str, data: UpdateCompanySchema
) -> CompanySchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        raise HttpException(403, "You don't have permission to update companies")

    # Check if user has access to this company
    if request.user.role == Role.MANAGER:
        user_offices = request.user.offices.values_list("office_id", flat=True)
        company = Company.objects.get(id=company_id)
        if company.office_id not in user_offices:
            raise HttpException(403, "You don't have access to this company")

    company = Company.objects.get(id=company_id)
    company.code = data.code
    company.name = data.name
    company.address = data.address
    company.phone = data.phone
    company.color_code = data.color_code
    company.save()

    return CompanySchema.from_orm(company)


# DELETE /api/orders/companies/{company_id}
@router.delete("/companies/{company_id}", auth=AuthKey, response=str)
def delete_company(request: HttpRequest, company_id: str):
    if request.user.role != Role.ADMIN:
        raise HttpException(403, "You don't have permission to delete companies")

    company = Company.objects.get(id=company_id)
    company.delete()
    return "OK"
