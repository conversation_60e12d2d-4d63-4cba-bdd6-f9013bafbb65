from typing import List, Optional, Dict, Any
from datetime import datetime
from ninja import Schema
from enum import Enum


class OrderHandlingStatusSchema(str, Enum):
    PENDING = "PENDING"
    ASSIGNED = "ASSIGNED"
    PROCESSING = "PROCESSING"
    CANCELLED = "CANCELLED"
    DELIVERED = "DELIVERED"


class CompanySchema(Schema):
    id: str
    office_id: str
    code: Optional[str]
    name: str
    address: str
    phone: str
    color_code: str
    created_at: datetime
    updated_at: datetime


class CreateCompanySchema(Schema):
    office_id: str
    code: Optional[str]
    name: str
    address: str
    phone: str
    color_code: str


class UpdateCompanySchema(Schema):
    code: Optional[str]
    name: str
    address: str
    phone: str
    color_code: str


class OrdersSheetSchema(Schema):
    id: str
    office_id: str
    name: str
    active: bool
    notes: str
    created_by_id: str
    created_at: datetime
    updated_at: datetime


class CreateOrdersSheetSchema(Schema):
    office_id: str
    name: str
    active: Optional[bool] = True
    notes: Optional[str] = ""


class UpdateOrdersSheetSchema(Schema):
    name: str
    active: bool
    notes: str


class OrdersSheetListResponseSchema(Schema):
    count: int
    next: Optional[str]
    previous: Optional[str]
    results: List[OrdersSheetSchema]


# Dashboard Analytics Schemas
class DashboardStatsSchema(Schema):
    total_orders: int
    total_amount: float
    collected_cash: float
    total_commission: float


class EmployeeStatsSchema(Schema):
    id: str
    name: str
    orders_count: int
    completed_orders_count: int
    delivered_orders_count: int
    cancelled_orders_count: int
    current_wallet_balance: float
    total_customer_payments: float


class DashboardEmployeesSchema(Schema):
    employees: List[EmployeeStatsSchema]


class ChartDataPointSchema(Schema):
    sheet_id: Optional[str]
    sheet_name: str
    revenue: float
    period: str  # "3_months", "1_month", "7_days"


class DashboardChartsSchema(Schema):
    chart_data: List[ChartDataPointSchema]


class CompanyChannelSchema(Schema):
    id: str
    merchant_id: str
    office_id: str
    company_id: Optional[str]
    name: str
    notes: str
    channel_whatsapp_number: str
    created_at: datetime
    updated_at: datetime


class CreateCompanyChannelSchema(Schema):
    merchant_id: str
    office_id: str
    company_id: Optional[str]
    name: str
    notes: str
    channel_whatsapp_number: str


class UpdateCompanyChannelSchema(Schema):
    company_id: Optional[str]
    name: str
    notes: str
    channel_whatsapp_number: str


class OrderStatusTemplateSchema(Schema):
    id: str
    office_id: Optional[str]
    name: str
    reason_template_text: str
    order_default_handling_status: Optional[OrderHandlingStatusSchema]
    just_delivery_commission_rate: bool
    commission_fixed_rate: Optional[float]
    percentage_of_order_total_price: Optional[float]
    created_at: datetime
    updated_at: datetime


class CreateOrderStatusTemplateSchema(Schema):
    office_id: Optional[str]
    name: str
    reason_template_text: str
    order_default_handling_status: Optional[OrderHandlingStatusSchema]
    just_delivery_commission_rate: bool
    commission_fixed_rate: Optional[float]
    percentage_of_order_total_price: Optional[float]


class UpdateOrderStatusTemplateSchema(Schema):
    name: str
    reason_template_text: str
    order_default_handling_status: Optional[OrderHandlingStatusSchema]
    just_delivery_commission_rate: bool
    commission_fixed_rate: Optional[float]
    percentage_of_order_total_price: Optional[float]


class OrderSchema(Schema):
    id: str
    office_id: str
    orders_sheet_id: Optional[str]
    orders_sheet: Optional[OrdersSheetSchema] = None  # Include full sheet details
    notes: str
    total_price: Optional[float]
    customer_name: str
    customer_phone: str
    customer_address: str
    customer_company_id: Optional[str]
    customer_company: Optional[CompanySchema] = None  # Include full company details
    breakable: bool
    deadline_date: Optional[datetime]
    commission_fixed_rate: Optional[float]
    assigned_to_id: Optional[str]
    assigned_at: Optional[datetime]
    final_customer_payment: Optional[float]
    handling_status: OrderHandlingStatusSchema
    completed_at: Optional[datetime]
    status_template_id: Optional[str]
    status_reason: Optional[str]
    created_at: datetime
    updated_at: datetime


class CreateOrderSchema(Schema):
    office_id: str
    orders_sheet_id: Optional[str] = None
    notes: Optional[str] = ""
    total_price: Optional[float] = 0
    customer_name: str
    customer_phone: str
    customer_address: Optional[str] = ""
    customer_company_id: Optional[str]
    company_code: Optional[str] = (
        ""  # Used to lookup company when customer_company_id is None
    )
    breakable: Optional[bool] = False
    deadline_date: Optional[datetime] = None
    commission_fixed_rate: Optional[float] = None
    assigned_to_id: Optional[str] = None
    final_customer_payment: Optional[float] = None
    handling_status: OrderHandlingStatusSchema = OrderHandlingStatusSchema.PENDING
    status_template_id: Optional[str]
    status_reason: Optional[str]


class UpdateOrderSchema(Schema):
    orders_sheet_id: Optional[str]
    notes: str
    total_price: Optional[float]
    customer_name: str
    customer_phone: str
    customer_address: str
    customer_company_id: Optional[str]
    breakable: bool
    deadline_date: Optional[datetime]
    commission_fixed_rate: Optional[float]
    assigned_to_id: Optional[str]
    final_customer_payment: Optional[float]
    handling_status: OrderHandlingStatusSchema
    status_template_id: Optional[str]
    status_reason: Optional[str]


class OrderStatusHistorySchema(Schema):
    id: str
    order_id: str
    status: OrderHandlingStatusSchema
    notes: str
    created_by_id: str
    created_at: datetime
    updated_at: datetime


class OrderAssigneeHistorySchema(Schema):
    id: str
    order_id: str
    assignee_id: str
    assigned_by_id: str
    created_at: datetime
    updated_at: datetime


# Response schemas for pagination
class CompanyListResponseSchema(Schema):
    companies: List[CompanySchema]
    total: int
    page: int
    page_size: int


class CompanyChannelListResponseSchema(Schema):
    channels: List[CompanyChannelSchema]
    total: int
    page: int
    page_size: int


class OrderStatusTemplateListResponseSchema(Schema):
    templates: List[OrderStatusTemplateSchema]
    total: int
    page: int
    page_size: int


class OrderListResponseSchema(Schema):
    orders: List[OrderSchema]
    total: int
    page: int
    page_size: int


class OrderStatusHistoryListResponseSchema(Schema):
    history: List[OrderStatusHistorySchema]
    total: int
    page: int
    page_size: int


class OrderAssigneeHistoryListResponseSchema(Schema):
    history: List[OrderAssigneeHistorySchema]
    total: int
    page: int
    page_size: int


class BulkCreateOrderResponseSchema(Schema):
    success: bool
    created_orders: List[OrderSchema]
    failed_orders: List[Dict[str, Any]]
    total_processed: int
    successful_count: int
    failed_count: int
    errors: List[str]
