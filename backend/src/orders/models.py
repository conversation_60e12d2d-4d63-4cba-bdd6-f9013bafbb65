from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from merchants.models import Office, Merchant
from core.utils import MyModel
from accounts.models import User


class OrderHandlingStatus(models.TextChoices):
    PENDING = "PENDING", "Pending"
    ASSIGNED = "ASSIGNED", "Assigned"
    PROCESSING = "PROCESSING", "Processing"
    CANCELLED = "CANCELLED", "Cancelled"
    DELIVERED = "DELIVERED", "Delivered"


class Company(MyModel):
    office = models.ForeignKey(
        Office, on_delete=models.CASCADE, related_name="companies"
    )
    code = models.CharField(max_length=20, unique=True, null=True, blank=True)
    name = models.CharField(max_length=255)
    address = models.TextField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    color_code = models.CharField(
        max_length=20,
        blank=True,
        help_text="Hex color code for the company",
    )

    def __str__(self):
        return self.name

    class Meta:
        db_table = "companies"
        verbose_name = "Company"
        verbose_name_plural = "Companies"


class CompanyChannel(MyModel):
    merchant = models.ForeignKey(
        Merchant, on_delete=models.CASCADE, related_name="company_channels"
    )
    office = models.ForeignKey(
        Office, on_delete=models.CASCADE, related_name="company_channels"
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="channels",
    )
    name = models.CharField(max_length=255)
    notes = models.TextField(blank=True)
    channel_whatsapp_number = models.CharField(
        max_length=255,
        help_text="WhatsApp number for the channel",
    )

    def __str__(self):
        return self.name

    class Meta:
        db_table = "company_channels"
        verbose_name = "Company Channel"
        verbose_name_plural = "Company Channels"


class OrderStatusTemplate(MyModel):
    office = models.ForeignKey(
        Office,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="order_status_templates",
    )
    name = models.CharField(max_length=255)
    reason_template_text = models.TextField(blank=True)
    order_default_handling_status = models.CharField(
        max_length=20, choices=OrderHandlingStatus.choices, null=True, blank=True
    )
    just_delivery_commission_rate = models.BooleanField(default=True)
    commission_fixed_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        null=True,
        blank=True,
        help_text="Percentage of the order total price, null if not applicable",
    )
    percentage_of_order_total_price = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        null=True,
        blank=True,
        help_text="Percentage of the order total price, null if not applicable",
    )

    def __str__(self):
        return self.name

    class Meta:
        db_table = "order_status_templates"
        verbose_name = "Order Status Template"
        verbose_name_plural = "Order Status Templates"


class OrdersSheet(MyModel):
    office = models.ForeignKey(
        Office, on_delete=models.CASCADE, related_name="orders_sheets"
    )
    name = models.CharField(max_length=255)
    active = models.BooleanField(default=True)
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="orders_sheets"
    )

    def __str__(self):
        return self.name

    class Meta:
        db_table = "orders_sheets"
        verbose_name = "Orders Sheet"
        verbose_name_plural = "Orders Sheets"


class Order(MyModel):
    orders_sheet = models.ForeignKey(
        OrdersSheet,
        on_delete=models.CASCADE,
        related_name="orders",
        null=True,
        blank=True,
    )
    office = models.ForeignKey(Office, on_delete=models.CASCADE, related_name="orders")
    # Customer information
    notes = models.TextField(blank=True)
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Amount customer should pay",
    )
    customer_name = models.CharField(max_length=255)
    customer_phone = models.CharField(
        max_length=500,
        help_text="List of customer phone numbers separated by `-`",
    )
    customer_address = models.TextField(blank=True)
    customer_company = models.ForeignKey(
        Company, on_delete=models.CASCADE, null=True, blank=True, related_name="orders"
    )
    breakable = models.BooleanField(
        default=False,
        help_text="Whether the order contains breakable items like glass",
    )
    deadline_date = models.DateTimeField(null=True, blank=True)
    # Commission information
    commission_fixed_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(1000)],
        null=True,
        blank=True,
        help_text="Fixed commission rate for the order",
    )
    # Assignee information
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="assigned_orders",
    )
    assigned_at = models.DateTimeField(null=True, blank=True)

    # After delivery information
    final_customer_payment = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Amount paid by the customer",
    )
    handling_status = models.CharField(
        max_length=20,
        choices=OrderHandlingStatus.choices,
        default=OrderHandlingStatus.PENDING,
    )
    completed_at = models.DateTimeField(null=True, blank=True)

    # Status information
    status_template = models.ForeignKey(
        OrderStatusTemplate,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="orders",
    )
    status_reason = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.customer_name} - {self.created_at}"

    def get_phone_numbers(self):
        """Parse and return list of phone numbers"""
        if self.customer_phone:
            return [
                phone.strip()
                for phone in self.customer_phone.split("-")
                if phone.strip()
            ]
        return []

    class Meta:
        db_table = "orders"
        verbose_name = "Order"
        verbose_name_plural = "Orders"


class OrderStatusHistory(MyModel):
    order = models.ForeignKey(
        Order, on_delete=models.CASCADE, related_name="status_history"
    )
    status = models.CharField(max_length=20, choices=OrderHandlingStatus.choices)
    notes = models.TextField(
        blank=True,
        help_text="Notes for the status change, will be the cancellation reason if the status is cancelled",
    )
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="order_status_changes"
    )

    class Meta:
        db_table = "order_status_histories"
        verbose_name = "Order Status History"
        verbose_name_plural = "Order Status Histories"


class OrderAssigneeHistory(MyModel):
    order = models.ForeignKey(
        Order, on_delete=models.CASCADE, related_name="assignee_history"
    )
    assignee = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="assigned_orders_history"
    )
    assigned_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="orders_assigned_by_history"
    )

    class Meta:
        db_table = "order_assignee_histories"
        verbose_name = "Order Assignee History"
        verbose_name_plural = "Order Assignee Histories"
