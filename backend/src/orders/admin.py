from django.contrib import admin
from .models import (
    Company,
    CompanyChannel,
    Order,
    OrderStatusHistory,
    OrderAssigneeHistory,
    OrderStatusTemplate,
)


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ("name", "code", "office", "phone", "color_code", "created_at")
    list_filter = ("office", "created_at")
    search_fields = ("name", "code", "phone")
    readonly_fields = ("created_at", "updated_at")
    list_per_page = 20


@admin.register(CompanyChannel)
class CompanyChannelAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "merchant",
        "office",
        "company",
        "channel_whatsapp_number",
        "created_at",
    )
    list_filter = ("merchant", "office", "company", "created_at")
    search_fields = ("name", "channel_whatsapp_number", "notes")
    readonly_fields = ("created_at", "updated_at")
    list_per_page = 20


@admin.register(OrderStatusTemplate)
class OrderStatusTemplateAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "office",
        "order_default_handling_status",
        "just_delivery_commission_rate",
        "commission_fixed_rate",
        "percentage_of_order_total_price",
        "created_at",
    )
    list_filter = (
        "office",
        "order_default_handling_status",
        "just_delivery_commission_rate",
        "created_at",
    )
    search_fields = ("name", "description")
    readonly_fields = ("created_at", "updated_at")
    list_per_page = 20


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = (
        "customer_name",
        "customer_company",
        "office",
        "total_price",
        "handling_status",
        "assigned_to",
        "deadline_date",
        "created_at",
    )
    list_filter = (
        "office",
        "customer_company",
        "handling_status",
        "assigned_to",
        "breakable",
        "created_at",
        "status_template",
    )
    search_fields = (
        "customer_name",
        "customer_phone",
        "customer_address",
        "notes",
    )
    readonly_fields = ("created_at", "updated_at", "assigned_at")
    list_per_page = 20

    fieldsets = (
        (
            "Basic Information",
            {
                "fields": (
                    "office",
                    "customer_name",
                    "customer_phone",
                    "customer_address",
                    "notes",
                )
            },
        ),
        (
            "Company & Pricing",
            {"fields": ("customer_company", "total_price", "commission_fixed_rate")},
        ),
        (
            "Delivery & Status",
            {
                "fields": (
                    "deadline_date",
                    "final_customer_payment",
                    "handling_status",
                    "status_template",
                    "status_reason",
                )
            },
        ),
        ("Assignment", {"fields": ("assigned_to", "assigned_at")}),
        ("Properties", {"fields": ("breakable",)}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    list_display = ("order", "status", "notes", "created_by", "created_at")
    list_filter = ("status", "created_by", "created_at")
    search_fields = ("order__customer_name",)
    readonly_fields = ("created_at", "updated_at")
    list_per_page = 20


@admin.register(OrderAssigneeHistory)
class OrderAssigneeHistoryAdmin(admin.ModelAdmin):
    list_display = ("order", "assignee", "assigned_by", "created_at")
    list_filter = ("assignee", "assigned_by", "created_at")
    search_fields = (
        "order__customer_name",
        "assignee__username",
        "assigned_by__username",
    )
    readonly_fields = ("created_at", "updated_at")
    list_per_page = 20
