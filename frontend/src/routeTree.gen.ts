/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as IndexRouteImport } from './routes/index'
import { Route as SheetsIndexRouteImport } from './routes/sheets/index'
import { Route as SettingsIndexRouteImport } from './routes/settings/index'
import { Route as OrdersIndexRouteImport } from './routes/orders/index'
import { Route as EmployeesIndexRouteImport } from './routes/employees/index'
import { Route as CompaniesIndexRouteImport } from './routes/companies/index'
import { Route as SheetsFormRouteImport } from './routes/sheets/form'
import { Route as OrdersFormRouteImport } from './routes/orders/form'
import { Route as OrdersBulkImportRouteImport } from './routes/orders/bulk-import'
import { Route as OrdersAssignRouteImport } from './routes/orders/assign'
import { Route as EmployeesFormRouteImport } from './routes/employees/form'
import { Route as CompaniesFormRouteImport } from './routes/companies/form'
import { Route as SettingsCancellation_templatesIndexRouteImport } from './routes/settings/cancellation_templates/index'
import { Route as CompaniesChannelsIndexRouteImport } from './routes/companies/channels/index'
import { Route as CompaniesChannelsFormRouteImport } from './routes/companies/channels/form'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const SheetsIndexRoute = SheetsIndexRouteImport.update({
  id: '/sheets/',
  path: '/sheets/',
  getParentRoute: () => rootRouteImport,
} as any)
const SettingsIndexRoute = SettingsIndexRouteImport.update({
  id: '/settings/',
  path: '/settings/',
  getParentRoute: () => rootRouteImport,
} as any)
const OrdersIndexRoute = OrdersIndexRouteImport.update({
  id: '/orders/',
  path: '/orders/',
  getParentRoute: () => rootRouteImport,
} as any)
const EmployeesIndexRoute = EmployeesIndexRouteImport.update({
  id: '/employees/',
  path: '/employees/',
  getParentRoute: () => rootRouteImport,
} as any)
const CompaniesIndexRoute = CompaniesIndexRouteImport.update({
  id: '/companies/',
  path: '/companies/',
  getParentRoute: () => rootRouteImport,
} as any)
const SheetsFormRoute = SheetsFormRouteImport.update({
  id: '/sheets/form',
  path: '/sheets/form',
  getParentRoute: () => rootRouteImport,
} as any)
const OrdersFormRoute = OrdersFormRouteImport.update({
  id: '/orders/form',
  path: '/orders/form',
  getParentRoute: () => rootRouteImport,
} as any)
const OrdersBulkImportRoute = OrdersBulkImportRouteImport.update({
  id: '/orders/bulk-import',
  path: '/orders/bulk-import',
  getParentRoute: () => rootRouteImport,
} as any)
const OrdersAssignRoute = OrdersAssignRouteImport.update({
  id: '/orders/assign',
  path: '/orders/assign',
  getParentRoute: () => rootRouteImport,
} as any)
const EmployeesFormRoute = EmployeesFormRouteImport.update({
  id: '/employees/form',
  path: '/employees/form',
  getParentRoute: () => rootRouteImport,
} as any)
const CompaniesFormRoute = CompaniesFormRouteImport.update({
  id: '/companies/form',
  path: '/companies/form',
  getParentRoute: () => rootRouteImport,
} as any)
const SettingsCancellation_templatesIndexRoute =
  SettingsCancellation_templatesIndexRouteImport.update({
    id: '/settings/cancellation_templates/',
    path: '/settings/cancellation_templates/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CompaniesChannelsIndexRoute = CompaniesChannelsIndexRouteImport.update({
  id: '/companies/channels/',
  path: '/companies/channels/',
  getParentRoute: () => rootRouteImport,
} as any)
const CompaniesChannelsFormRoute = CompaniesChannelsFormRouteImport.update({
  id: '/companies/channels/form',
  path: '/companies/channels/form',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/companies/form': typeof CompaniesFormRoute
  '/employees/form': typeof EmployeesFormRoute
  '/orders/assign': typeof OrdersAssignRoute
  '/orders/bulk-import': typeof OrdersBulkImportRoute
  '/orders/form': typeof OrdersFormRoute
  '/sheets/form': typeof SheetsFormRoute
  '/companies': typeof CompaniesIndexRoute
  '/employees': typeof EmployeesIndexRoute
  '/orders': typeof OrdersIndexRoute
  '/settings': typeof SettingsIndexRoute
  '/sheets': typeof SheetsIndexRoute
  '/companies/channels/form': typeof CompaniesChannelsFormRoute
  '/companies/channels': typeof CompaniesChannelsIndexRoute
  '/settings/cancellation_templates': typeof SettingsCancellation_templatesIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/companies/form': typeof CompaniesFormRoute
  '/employees/form': typeof EmployeesFormRoute
  '/orders/assign': typeof OrdersAssignRoute
  '/orders/bulk-import': typeof OrdersBulkImportRoute
  '/orders/form': typeof OrdersFormRoute
  '/sheets/form': typeof SheetsFormRoute
  '/companies': typeof CompaniesIndexRoute
  '/employees': typeof EmployeesIndexRoute
  '/orders': typeof OrdersIndexRoute
  '/settings': typeof SettingsIndexRoute
  '/sheets': typeof SheetsIndexRoute
  '/companies/channels/form': typeof CompaniesChannelsFormRoute
  '/companies/channels': typeof CompaniesChannelsIndexRoute
  '/settings/cancellation_templates': typeof SettingsCancellation_templatesIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/companies/form': typeof CompaniesFormRoute
  '/employees/form': typeof EmployeesFormRoute
  '/orders/assign': typeof OrdersAssignRoute
  '/orders/bulk-import': typeof OrdersBulkImportRoute
  '/orders/form': typeof OrdersFormRoute
  '/sheets/form': typeof SheetsFormRoute
  '/companies/': typeof CompaniesIndexRoute
  '/employees/': typeof EmployeesIndexRoute
  '/orders/': typeof OrdersIndexRoute
  '/settings/': typeof SettingsIndexRoute
  '/sheets/': typeof SheetsIndexRoute
  '/companies/channels/form': typeof CompaniesChannelsFormRoute
  '/companies/channels/': typeof CompaniesChannelsIndexRoute
  '/settings/cancellation_templates/': typeof SettingsCancellation_templatesIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/dashboard'
    | '/login'
    | '/companies/form'
    | '/employees/form'
    | '/orders/assign'
    | '/orders/bulk-import'
    | '/orders/form'
    | '/sheets/form'
    | '/companies'
    | '/employees'
    | '/orders'
    | '/settings'
    | '/sheets'
    | '/companies/channels/form'
    | '/companies/channels'
    | '/settings/cancellation_templates'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/dashboard'
    | '/login'
    | '/companies/form'
    | '/employees/form'
    | '/orders/assign'
    | '/orders/bulk-import'
    | '/orders/form'
    | '/sheets/form'
    | '/companies'
    | '/employees'
    | '/orders'
    | '/settings'
    | '/sheets'
    | '/companies/channels/form'
    | '/companies/channels'
    | '/settings/cancellation_templates'
  id:
    | '__root__'
    | '/'
    | '/dashboard'
    | '/login'
    | '/companies/form'
    | '/employees/form'
    | '/orders/assign'
    | '/orders/bulk-import'
    | '/orders/form'
    | '/sheets/form'
    | '/companies/'
    | '/employees/'
    | '/orders/'
    | '/settings/'
    | '/sheets/'
    | '/companies/channels/form'
    | '/companies/channels/'
    | '/settings/cancellation_templates/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRoute: typeof DashboardRoute
  LoginRoute: typeof LoginRoute
  CompaniesFormRoute: typeof CompaniesFormRoute
  EmployeesFormRoute: typeof EmployeesFormRoute
  OrdersAssignRoute: typeof OrdersAssignRoute
  OrdersBulkImportRoute: typeof OrdersBulkImportRoute
  OrdersFormRoute: typeof OrdersFormRoute
  SheetsFormRoute: typeof SheetsFormRoute
  CompaniesIndexRoute: typeof CompaniesIndexRoute
  EmployeesIndexRoute: typeof EmployeesIndexRoute
  OrdersIndexRoute: typeof OrdersIndexRoute
  SettingsIndexRoute: typeof SettingsIndexRoute
  SheetsIndexRoute: typeof SheetsIndexRoute
  CompaniesChannelsFormRoute: typeof CompaniesChannelsFormRoute
  CompaniesChannelsIndexRoute: typeof CompaniesChannelsIndexRoute
  SettingsCancellation_templatesIndexRoute: typeof SettingsCancellation_templatesIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/sheets/': {
      id: '/sheets/'
      path: '/sheets'
      fullPath: '/sheets'
      preLoaderRoute: typeof SheetsIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings/': {
      id: '/settings/'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/orders/': {
      id: '/orders/'
      path: '/orders'
      fullPath: '/orders'
      preLoaderRoute: typeof OrdersIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/employees/': {
      id: '/employees/'
      path: '/employees'
      fullPath: '/employees'
      preLoaderRoute: typeof EmployeesIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/companies/': {
      id: '/companies/'
      path: '/companies'
      fullPath: '/companies'
      preLoaderRoute: typeof CompaniesIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/sheets/form': {
      id: '/sheets/form'
      path: '/sheets/form'
      fullPath: '/sheets/form'
      preLoaderRoute: typeof SheetsFormRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/orders/form': {
      id: '/orders/form'
      path: '/orders/form'
      fullPath: '/orders/form'
      preLoaderRoute: typeof OrdersFormRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/orders/bulk-import': {
      id: '/orders/bulk-import'
      path: '/orders/bulk-import'
      fullPath: '/orders/bulk-import'
      preLoaderRoute: typeof OrdersBulkImportRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/orders/assign': {
      id: '/orders/assign'
      path: '/orders/assign'
      fullPath: '/orders/assign'
      preLoaderRoute: typeof OrdersAssignRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/employees/form': {
      id: '/employees/form'
      path: '/employees/form'
      fullPath: '/employees/form'
      preLoaderRoute: typeof EmployeesFormRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/companies/form': {
      id: '/companies/form'
      path: '/companies/form'
      fullPath: '/companies/form'
      preLoaderRoute: typeof CompaniesFormRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings/cancellation_templates/': {
      id: '/settings/cancellation_templates/'
      path: '/settings/cancellation_templates'
      fullPath: '/settings/cancellation_templates'
      preLoaderRoute: typeof SettingsCancellation_templatesIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/companies/channels/': {
      id: '/companies/channels/'
      path: '/companies/channels'
      fullPath: '/companies/channels'
      preLoaderRoute: typeof CompaniesChannelsIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/companies/channels/form': {
      id: '/companies/channels/form'
      path: '/companies/channels/form'
      fullPath: '/companies/channels/form'
      preLoaderRoute: typeof CompaniesChannelsFormRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRoute: DashboardRoute,
  LoginRoute: LoginRoute,
  CompaniesFormRoute: CompaniesFormRoute,
  EmployeesFormRoute: EmployeesFormRoute,
  OrdersAssignRoute: OrdersAssignRoute,
  OrdersBulkImportRoute: OrdersBulkImportRoute,
  OrdersFormRoute: OrdersFormRoute,
  SheetsFormRoute: SheetsFormRoute,
  CompaniesIndexRoute: CompaniesIndexRoute,
  EmployeesIndexRoute: EmployeesIndexRoute,
  OrdersIndexRoute: OrdersIndexRoute,
  SettingsIndexRoute: SettingsIndexRoute,
  SheetsIndexRoute: SheetsIndexRoute,
  CompaniesChannelsFormRoute: CompaniesChannelsFormRoute,
  CompaniesChannelsIndexRoute: CompaniesChannelsIndexRoute,
  SettingsCancellation_templatesIndexRoute:
    SettingsCancellation_templatesIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
