// This file is auto-generated by @hey-api/openapi-ts

/**
 * LoginResponseSchema
 */
export type LoginResponseSchema = {
    /**
     * Token
     */
    token: string;
    user: UserSchema;
    office: UserOfficeSchema;
};

/**
 * UserOfficeSchema
 */
export type UserOfficeSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Name
     */
    name: string;
    /**
     * Slug
     */
    slug: string;
    /**
     * Address
     */
    address: string;
};

/**
 * UserSchema
 */
export type UserSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * First Name
     */
    first_name: string;
    /**
     * Last Name
     */
    last_name: string;
    /**
     * Username
     */
    username: string;
    /**
     * Email
     */
    email: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Role
     */
    role: string;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Current Location Lat
     */
    current_location_lat: number | null;
    /**
     * Current Location Lng
     */
    current_location_lng: number | null;
};

/**
 * LoginSchema
 */
export type LoginSchema = {
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Password
     */
    password: string;
    /**
     * Office Id
     */
    office_id: string;
};

/**
 * UserListResponseSchema
 */
export type UserListResponseSchema = {
    /**
     * Users
     */
    users: Array<UserSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * CreateUserSchema
 */
export type CreateUserSchema = {
    /**
     * First Name
     */
    first_name: string;
    /**
     * Last Name
     */
    last_name: string;
    /**
     * Username
     */
    username: string;
    /**
     * Email
     */
    email: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Role
     */
    role: string;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number;
    /**
     * Password
     */
    password: string;
};

/**
 * UpdateUserSchema
 */
export type UpdateUserSchema = {
    /**
     * First Name
     */
    first_name: string;
    /**
     * Last Name
     */
    last_name: string;
    /**
     * Username
     */
    username: string;
    /**
     * Email
     */
    email: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Role
     */
    role: string;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number;
    /**
     * Current Location Lat
     */
    current_location_lat: number;
    /**
     * Current Location Lng
     */
    current_location_lng: number;
};

/**
 * LocationUpdateSchema
 */
export type LocationUpdateSchema = {
    /**
     * Latitude
     */
    latitude: number;
    /**
     * Longitude
     */
    longitude: number;
};

/**
 * LocationHistoryResponseSchema
 */
export type LocationHistoryResponseSchema = {
    /**
     * Locations
     */
    locations: Array<LocationHistorySchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * LocationHistorySchema
 */
export type LocationHistorySchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Location Lat
     */
    location_lat: number;
    /**
     * Location Lng
     */
    location_lng: number;
    /**
     * Update Time
     */
    update_time: string;
};

/**
 * UserWalletSchema
 */
export type UserWalletSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Balance
     */
    balance: number;
    /**
     * Last Update
     */
    last_update: string;
};

/**
 * UserPointsSchema
 */
export type UserPointsSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Points
     */
    points: number;
    /**
     * Last Update
     */
    last_update: string;
};

/**
 * OfficeListResponseSchema
 */
export type OfficeListResponseSchema = {
    /**
     * Offices
     */
    offices: Array<OfficeSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OfficeSchema
 */
export type OfficeSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Name
     */
    name: string;
    /**
     * Slug
     */
    slug: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Email
     */
    email: string;
};

/**
 * CreateOfficeSchema
 */
export type CreateOfficeSchema = {
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Name
     */
    name: string;
    /**
     * Slug
     */
    slug: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Email
     */
    email: string;
};

/**
 * UpdateOfficeSchema
 */
export type UpdateOfficeSchema = {
    /**
     * Name
     */
    name: string;
    /**
     * Slug
     */
    slug: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Email
     */
    email: string;
};

/**
 * OfficeEmployeeListResponseSchema
 */
export type OfficeEmployeeListResponseSchema = {
    /**
     * Employees
     */
    employees: Array<OfficeEmployeeSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OfficeEmployeeSchema
 */
export type OfficeEmployeeSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * User Id
     */
    user_id: string;
    user: UserSchema;
};

/**
 * CreateOfficeEmployeeSchema
 */
export type CreateOfficeEmployeeSchema = {
    /**
     * Office Id
     */
    office_id: string;
    /**
     * User Id
     */
    user_id: string;
};

/**
 * CompanyListResponseSchema
 */
export type CompanyListResponseSchema = {
    /**
     * Companies
     */
    companies: Array<CompanySchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * CompanySchema
 */
export type CompanySchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Code
     */
    code: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone
     */
    phone: string;
    /**
     * Color Code
     */
    color_code: string;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * CreateCompanySchema
 */
export type CreateCompanySchema = {
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Code
     */
    code: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone
     */
    phone: string;
    /**
     * Color Code
     */
    color_code: string;
};

/**
 * UpdateCompanySchema
 */
export type UpdateCompanySchema = {
    /**
     * Code
     */
    code: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone
     */
    phone: string;
    /**
     * Color Code
     */
    color_code: string;
};

/**
 * CompanyChannelListResponseSchema
 */
export type CompanyChannelListResponseSchema = {
    /**
     * Channels
     */
    channels: Array<CompanyChannelSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * CompanyChannelSchema
 */
export type CompanyChannelSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Company Id
     */
    company_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Notes
     */
    notes: string;
    /**
     * Channel Whatsapp Number
     */
    channel_whatsapp_number: string;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * CreateCompanyChannelSchema
 */
export type CreateCompanyChannelSchema = {
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Company Id
     */
    company_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Notes
     */
    notes: string;
    /**
     * Channel Whatsapp Number
     */
    channel_whatsapp_number: string;
};

/**
 * UpdateCompanyChannelSchema
 */
export type UpdateCompanyChannelSchema = {
    /**
     * Company Id
     */
    company_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Notes
     */
    notes: string;
    /**
     * Channel Whatsapp Number
     */
    channel_whatsapp_number: string;
};

/**
 * OrderHandlingStatusSchema
 */
export type OrderHandlingStatusSchema = 'PENDING' | 'ASSIGNED' | 'PROCESSING' | 'CANCELLED' | 'DELIVERED';

/**
 * OrderStatusTemplateListResponseSchema
 */
export type OrderStatusTemplateListResponseSchema = {
    /**
     * Templates
     */
    templates: Array<OrderStatusTemplateSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OrderStatusTemplateSchema
 */
export type OrderStatusTemplateSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Office Id
     */
    office_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Reason Template Text
     */
    reason_template_text: string;
    order_default_handling_status: OrderHandlingStatusSchema | null;
    /**
     * Just Delivery Commission Rate
     */
    just_delivery_commission_rate: boolean;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Percentage Of Order Total Price
     */
    percentage_of_order_total_price: number | null;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * CreateOrderStatusTemplateSchema
 */
export type CreateOrderStatusTemplateSchema = {
    /**
     * Office Id
     */
    office_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Reason Template Text
     */
    reason_template_text: string;
    order_default_handling_status: OrderHandlingStatusSchema | null;
    /**
     * Just Delivery Commission Rate
     */
    just_delivery_commission_rate: boolean;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Percentage Of Order Total Price
     */
    percentage_of_order_total_price: number | null;
};

/**
 * UpdateOrderStatusTemplateSchema
 */
export type UpdateOrderStatusTemplateSchema = {
    /**
     * Name
     */
    name: string;
    /**
     * Reason Template Text
     */
    reason_template_text: string;
    order_default_handling_status: OrderHandlingStatusSchema | null;
    /**
     * Just Delivery Commission Rate
     */
    just_delivery_commission_rate: boolean;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Percentage Of Order Total Price
     */
    percentage_of_order_total_price: number | null;
};

/**
 * OrderListResponseSchema
 */
export type OrderListResponseSchema = {
    /**
     * Orders
     */
    orders: Array<OrderSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OrderSchema
 */
export type OrderSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Orders Sheet Id
     */
    orders_sheet_id: string | null;
    orders_sheet?: OrdersSheetSchema | null;
    /**
     * Notes
     */
    notes: string;
    /**
     * Total Price
     */
    total_price: number | null;
    /**
     * Customer Name
     */
    customer_name: string;
    /**
     * Customer Phone
     */
    customer_phone: string;
    /**
     * Customer Address
     */
    customer_address: string;
    /**
     * Customer Company Id
     */
    customer_company_id: string | null;
    customer_company?: CompanySchema | null;
    /**
     * Breakable
     */
    breakable: boolean;
    /**
     * Deadline Date
     */
    deadline_date: string | null;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Assigned To Id
     */
    assigned_to_id: string | null;
    /**
     * Assigned At
     */
    assigned_at: string | null;
    /**
     * Final Customer Payment
     */
    final_customer_payment: number | null;
    handling_status: OrderHandlingStatusSchema;
    /**
     * Completed At
     */
    completed_at: string | null;
    /**
     * Status Template Id
     */
    status_template_id: string | null;
    /**
     * Status Reason
     */
    status_reason: string | null;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * OrdersSheetSchema
 */
export type OrdersSheetSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Name
     */
    name: string;
    /**
     * Active
     */
    active: boolean;
    /**
     * Notes
     */
    notes: string;
    /**
     * Created By Id
     */
    created_by_id: string;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * CreateOrderSchema
 */
export type CreateOrderSchema = {
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Orders Sheet Id
     */
    orders_sheet_id?: string | null;
    /**
     * Notes
     */
    notes?: string | null;
    /**
     * Total Price
     */
    total_price?: number | null;
    /**
     * Customer Name
     */
    customer_name: string;
    /**
     * Customer Phone
     */
    customer_phone: string;
    /**
     * Customer Address
     */
    customer_address?: string | null;
    /**
     * Customer Company Id
     */
    customer_company_id: string | null;
    /**
     * Company Code
     */
    company_code?: string | null;
    /**
     * Breakable
     */
    breakable?: boolean | null;
    /**
     * Deadline Date
     */
    deadline_date?: string | null;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate?: number | null;
    /**
     * Assigned To Id
     */
    assigned_to_id?: string | null;
    /**
     * Final Customer Payment
     */
    final_customer_payment?: number | null;
    handling_status?: OrderHandlingStatusSchema;
    /**
     * Status Template Id
     */
    status_template_id: string | null;
    /**
     * Status Reason
     */
    status_reason: string | null;
};

/**
 * BulkCreateOrderResponseSchema
 */
export type BulkCreateOrderResponseSchema = {
    /**
     * Success
     */
    success: boolean;
    /**
     * Created Orders
     */
    created_orders: Array<OrderSchema>;
    /**
     * Failed Orders
     */
    failed_orders: Array<{
        [key: string]: unknown;
    }>;
    /**
     * Total Processed
     */
    total_processed: number;
    /**
     * Successful Count
     */
    successful_count: number;
    /**
     * Failed Count
     */
    failed_count: number;
    /**
     * Errors
     */
    errors: Array<string>;
};

/**
 * UpdateOrderSchema
 */
export type UpdateOrderSchema = {
    /**
     * Orders Sheet Id
     */
    orders_sheet_id: string | null;
    /**
     * Notes
     */
    notes: string;
    /**
     * Total Price
     */
    total_price: number | null;
    /**
     * Customer Name
     */
    customer_name: string;
    /**
     * Customer Phone
     */
    customer_phone: string;
    /**
     * Customer Address
     */
    customer_address: string;
    /**
     * Customer Company Id
     */
    customer_company_id: string | null;
    /**
     * Breakable
     */
    breakable: boolean;
    /**
     * Deadline Date
     */
    deadline_date: string | null;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Assigned To Id
     */
    assigned_to_id: string | null;
    /**
     * Final Customer Payment
     */
    final_customer_payment: number | null;
    handling_status: OrderHandlingStatusSchema;
    /**
     * Status Template Id
     */
    status_template_id: string | null;
    /**
     * Status Reason
     */
    status_reason: string | null;
};

/**
 * OrdersSheetListResponseSchema
 */
export type OrdersSheetListResponseSchema = {
    /**
     * Count
     */
    count: number;
    /**
     * Next
     */
    next: string | null;
    /**
     * Previous
     */
    previous: string | null;
    /**
     * Results
     */
    results: Array<OrdersSheetSchema>;
};

/**
 * CreateOrdersSheetSchema
 */
export type CreateOrdersSheetSchema = {
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Name
     */
    name: string;
    /**
     * Active
     */
    active?: boolean | null;
    /**
     * Notes
     */
    notes?: string | null;
};

/**
 * UpdateOrdersSheetSchema
 */
export type UpdateOrdersSheetSchema = {
    /**
     * Name
     */
    name: string;
    /**
     * Active
     */
    active: boolean;
    /**
     * Notes
     */
    notes: string;
};

/**
 * DashboardStatsSchema
 */
export type DashboardStatsSchema = {
    /**
     * Total Orders
     */
    total_orders: number;
    /**
     * Total Amount
     */
    total_amount: number;
    /**
     * Collected Cash
     */
    collected_cash: number;
    /**
     * Total Commission
     */
    total_commission: number;
};

/**
 * DashboardEmployeesSchema
 */
export type DashboardEmployeesSchema = {
    /**
     * Employees
     */
    employees: Array<EmployeeStatsSchema>;
};

/**
 * EmployeeStatsSchema
 */
export type EmployeeStatsSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Name
     */
    name: string;
    /**
     * Orders Count
     */
    orders_count: number;
    /**
     * Completed Orders Count
     */
    completed_orders_count: number;
    /**
     * Delivered Orders Count
     */
    delivered_orders_count: number;
    /**
     * Cancelled Orders Count
     */
    cancelled_orders_count: number;
    /**
     * Current Wallet Balance
     */
    current_wallet_balance: number;
    /**
     * Total Customer Payments
     */
    total_customer_payments: number;
};

/**
 * ChartDataPointSchema
 */
export type ChartDataPointSchema = {
    /**
     * Sheet Id
     */
    sheet_id: string | null;
    /**
     * Sheet Name
     */
    sheet_name: string;
    /**
     * Revenue
     */
    revenue: number;
    /**
     * Period
     */
    period: string;
};

/**
 * DashboardChartsSchema
 */
export type DashboardChartsSchema = {
    /**
     * Chart Data
     */
    chart_data: Array<ChartDataPointSchema>;
};

/**
 * OrderStatusHistoryListResponseSchema
 */
export type OrderStatusHistoryListResponseSchema = {
    /**
     * History
     */
    history: Array<OrderStatusHistorySchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OrderStatusHistorySchema
 */
export type OrderStatusHistorySchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Order Id
     */
    order_id: string;
    status: OrderHandlingStatusSchema;
    /**
     * Notes
     */
    notes: string;
    /**
     * Created By Id
     */
    created_by_id: string;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * OrderAssigneeHistoryListResponseSchema
 */
export type OrderAssigneeHistoryListResponseSchema = {
    /**
     * History
     */
    history: Array<OrderAssigneeHistorySchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OrderAssigneeHistorySchema
 */
export type OrderAssigneeHistorySchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Order Id
     */
    order_id: string;
    /**
     * Assignee Id
     */
    assignee_id: string;
    /**
     * Assigned By Id
     */
    assigned_by_id: string;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

export type AccountsApisLoginData = {
    body: LoginSchema;
    path?: never;
    query?: never;
    url: '/api/accounts/login';
};

export type AccountsApisLoginResponses = {
    /**
     * OK
     */
    200: LoginResponseSchema;
};

export type AccountsApisLoginResponse = AccountsApisLoginResponses[keyof AccountsApisLoginResponses];

export type AccountsApisLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/logout';
};

export type AccountsApisLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type AccountsApisGetMeData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/me';
};

export type AccountsApisGetMeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type AccountsApisListUsersData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Role
         */
        role?: string;
        /**
         * Office Id
         */
        office_id?: string;
    };
    url: '/api/accounts/';
};

export type AccountsApisListUsersResponses = {
    /**
     * OK
     */
    200: UserListResponseSchema;
};

export type AccountsApisListUsersResponse = AccountsApisListUsersResponses[keyof AccountsApisListUsersResponses];

export type AccountsApisCreateUserData = {
    body: CreateUserSchema;
    path?: never;
    query?: never;
    url: '/api/accounts/';
};

export type AccountsApisCreateUserResponses = {
    /**
     * OK
     */
    200: UserSchema;
};

export type AccountsApisCreateUserResponse = AccountsApisCreateUserResponses[keyof AccountsApisCreateUserResponses];

export type AccountsApisDeleteUserData = {
    body?: never;
    path: {
        /**
         * User Id
         */
        user_id: string;
    };
    query?: never;
    url: '/api/accounts/{user_id}';
};

export type AccountsApisDeleteUserResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type AccountsApisDeleteUserResponse = AccountsApisDeleteUserResponses[keyof AccountsApisDeleteUserResponses];

export type AccountsApisGetUserData = {
    body?: never;
    path: {
        /**
         * User Id
         */
        user_id: string;
    };
    query?: never;
    url: '/api/accounts/{user_id}';
};

export type AccountsApisGetUserResponses = {
    /**
     * OK
     */
    200: UserSchema;
};

export type AccountsApisGetUserResponse = AccountsApisGetUserResponses[keyof AccountsApisGetUserResponses];

export type AccountsApisUpdateUserData = {
    body: UpdateUserSchema;
    path: {
        /**
         * User Id
         */
        user_id: string;
    };
    query?: never;
    url: '/api/accounts/{user_id}';
};

export type AccountsApisUpdateUserResponses = {
    /**
     * OK
     */
    200: UserSchema;
};

export type AccountsApisUpdateUserResponse = AccountsApisUpdateUserResponses[keyof AccountsApisUpdateUserResponses];

export type AccountsApisUpdateLocationData = {
    body: LocationUpdateSchema;
    path?: never;
    query?: never;
    url: '/api/accounts/me/location';
};

export type AccountsApisUpdateLocationResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type AccountsApisUpdateLocationResponse = AccountsApisUpdateLocationResponses[keyof AccountsApisUpdateLocationResponses];

export type AccountsApisGetLocationHistoryData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
    };
    url: '/api/accounts/me/location/history';
};

export type AccountsApisGetLocationHistoryResponses = {
    /**
     * OK
     */
    200: LocationHistoryResponseSchema;
};

export type AccountsApisGetLocationHistoryResponse = AccountsApisGetLocationHistoryResponses[keyof AccountsApisGetLocationHistoryResponses];

export type AccountsApisGetMyOfficesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/me/offices';
};

export type AccountsApisGetMyOfficesResponses = {
    /**
     * OK
     */
    200: UserOfficeSchema;
};

export type AccountsApisGetMyOfficesResponse = AccountsApisGetMyOfficesResponses[keyof AccountsApisGetMyOfficesResponses];

export type AccountsApisGetMeWalletData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/me/wallet';
};

export type AccountsApisGetMeWalletResponses = {
    /**
     * OK
     */
    200: UserWalletSchema;
};

export type AccountsApisGetMeWalletResponse = AccountsApisGetMeWalletResponses[keyof AccountsApisGetMeWalletResponses];

export type AccountsApisUpdateMeWalletData = {
    body?: never;
    path?: never;
    query: {
        /**
         * Amount
         */
        amount: number;
    };
    url: '/api/accounts/me/wallet';
};

export type AccountsApisUpdateMeWalletResponses = {
    /**
     * OK
     */
    200: UserWalletSchema;
};

export type AccountsApisUpdateMeWalletResponse = AccountsApisUpdateMeWalletResponses[keyof AccountsApisUpdateMeWalletResponses];

export type AccountsApisGetMePointsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/me/points';
};

export type AccountsApisGetMePointsResponses = {
    /**
     * OK
     */
    200: UserPointsSchema;
};

export type AccountsApisGetMePointsResponse = AccountsApisGetMePointsResponses[keyof AccountsApisGetMePointsResponses];

export type AccountsApisAddPointsData = {
    body?: never;
    path?: never;
    query: {
        /**
         * Amount
         */
        amount: number;
    };
    url: '/api/accounts/me/points';
};

export type AccountsApisAddPointsResponses = {
    /**
     * OK
     */
    200: UserPointsSchema;
};

export type AccountsApisAddPointsResponse = AccountsApisAddPointsResponses[keyof AccountsApisAddPointsResponses];

export type MerchantsApisListOfficesData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Merchant Id
         */
        merchant_id?: string;
    };
    url: '/api/merchants/offices/';
};

export type MerchantsApisListOfficesResponses = {
    /**
     * OK
     */
    200: OfficeListResponseSchema;
};

export type MerchantsApisListOfficesResponse = MerchantsApisListOfficesResponses[keyof MerchantsApisListOfficesResponses];

export type MerchantsApisCreateOfficeData = {
    body: CreateOfficeSchema;
    path?: never;
    query?: never;
    url: '/api/merchants/offices/';
};

export type MerchantsApisCreateOfficeResponses = {
    /**
     * OK
     */
    200: OfficeSchema;
};

export type MerchantsApisCreateOfficeResponse = MerchantsApisCreateOfficeResponses[keyof MerchantsApisCreateOfficeResponses];

export type MerchantsApisDeleteOfficeData = {
    body?: never;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}';
};

export type MerchantsApisDeleteOfficeResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type MerchantsApisDeleteOfficeResponse = MerchantsApisDeleteOfficeResponses[keyof MerchantsApisDeleteOfficeResponses];

export type MerchantsApisGetOfficeData = {
    body?: never;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}';
};

export type MerchantsApisGetOfficeResponses = {
    /**
     * OK
     */
    200: OfficeSchema;
};

export type MerchantsApisGetOfficeResponse = MerchantsApisGetOfficeResponses[keyof MerchantsApisGetOfficeResponses];

export type MerchantsApisUpdateOfficeData = {
    body: UpdateOfficeSchema;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}';
};

export type MerchantsApisUpdateOfficeResponses = {
    /**
     * OK
     */
    200: OfficeSchema;
};

export type MerchantsApisUpdateOfficeResponse = MerchantsApisUpdateOfficeResponses[keyof MerchantsApisUpdateOfficeResponses];

export type MerchantsApisListEmployeesOfficesByPhoneNumberData = {
    body?: never;
    path?: never;
    query: {
        /**
         * Employee Phone Number
         */
        employee_phone_number: string;
    };
    url: '/api/merchants/offices/get-by-phone-number/';
};

export type MerchantsApisListEmployeesOfficesByPhoneNumberResponses = {
    /**
     * OK
     */
    200: OfficeListResponseSchema;
};

export type MerchantsApisListEmployeesOfficesByPhoneNumberResponse = MerchantsApisListEmployeesOfficesByPhoneNumberResponses[keyof MerchantsApisListEmployeesOfficesByPhoneNumberResponses];

export type MerchantsApisListOfficeEmployeesData = {
    body?: never;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
    };
    url: '/api/merchants/offices/{office_id}/employees/';
};

export type MerchantsApisListOfficeEmployeesResponses = {
    /**
     * OK
     */
    200: OfficeEmployeeListResponseSchema;
};

export type MerchantsApisListOfficeEmployeesResponse = MerchantsApisListOfficeEmployeesResponses[keyof MerchantsApisListOfficeEmployeesResponses];

export type MerchantsApisAddOfficeEmployeeData = {
    body: CreateOfficeEmployeeSchema;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}/employees/';
};

export type MerchantsApisAddOfficeEmployeeResponses = {
    /**
     * OK
     */
    200: OfficeEmployeeSchema;
};

export type MerchantsApisAddOfficeEmployeeResponse = MerchantsApisAddOfficeEmployeeResponses[keyof MerchantsApisAddOfficeEmployeeResponses];

export type MerchantsApisRemoveOfficeEmployeeData = {
    body?: never;
    path: {
        /**
         * Office Id
         */
        office_id: string;
        /**
         * Employee Id
         */
        employee_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}/employees/{employee_id}';
};

export type MerchantsApisRemoveOfficeEmployeeResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type MerchantsApisRemoveOfficeEmployeeResponse = MerchantsApisRemoveOfficeEmployeeResponses[keyof MerchantsApisRemoveOfficeEmployeeResponses];

export type OrdersApisCompaniesListCompaniesData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Office Id
         */
        office_id?: string | null;
    };
    url: '/api/orders/companies/';
};

export type OrdersApisCompaniesListCompaniesResponses = {
    /**
     * OK
     */
    200: CompanyListResponseSchema;
};

export type OrdersApisCompaniesListCompaniesResponse = OrdersApisCompaniesListCompaniesResponses[keyof OrdersApisCompaniesListCompaniesResponses];

export type OrdersApisCompaniesCreateCompanyData = {
    body: CreateCompanySchema;
    path?: never;
    query?: never;
    url: '/api/orders/companies/';
};

export type OrdersApisCompaniesCreateCompanyResponses = {
    /**
     * OK
     */
    200: CompanySchema;
};

export type OrdersApisCompaniesCreateCompanyResponse = OrdersApisCompaniesCreateCompanyResponses[keyof OrdersApisCompaniesCreateCompanyResponses];

export type OrdersApisCompaniesDeleteCompanyData = {
    body?: never;
    path: {
        /**
         * Company Id
         */
        company_id: string;
    };
    query?: never;
    url: '/api/orders/companies/{company_id}';
};

export type OrdersApisCompaniesDeleteCompanyResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type OrdersApisCompaniesDeleteCompanyResponse = OrdersApisCompaniesDeleteCompanyResponses[keyof OrdersApisCompaniesDeleteCompanyResponses];

export type OrdersApisCompaniesGetCompanyData = {
    body?: never;
    path: {
        /**
         * Company Id
         */
        company_id: string;
    };
    query?: never;
    url: '/api/orders/companies/{company_id}';
};

export type OrdersApisCompaniesGetCompanyResponses = {
    /**
     * OK
     */
    200: CompanySchema;
};

export type OrdersApisCompaniesGetCompanyResponse = OrdersApisCompaniesGetCompanyResponses[keyof OrdersApisCompaniesGetCompanyResponses];

export type OrdersApisCompaniesUpdateCompanyData = {
    body: UpdateCompanySchema;
    path: {
        /**
         * Company Id
         */
        company_id: string;
    };
    query?: never;
    url: '/api/orders/companies/{company_id}';
};

export type OrdersApisCompaniesUpdateCompanyResponses = {
    /**
     * OK
     */
    200: CompanySchema;
};

export type OrdersApisCompaniesUpdateCompanyResponse = OrdersApisCompaniesUpdateCompanyResponses[keyof OrdersApisCompaniesUpdateCompanyResponses];

export type OrdersApisCompanyChannelsListCompanyChannelsData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Merchant Id
         */
        merchant_id?: string | null;
        /**
         * Office Id
         */
        office_id?: string | null;
        /**
         * Company Id
         */
        company_id?: string | null;
    };
    url: '/api/orders/company-channels/';
};

export type OrdersApisCompanyChannelsListCompanyChannelsResponses = {
    /**
     * OK
     */
    200: CompanyChannelListResponseSchema;
};

export type OrdersApisCompanyChannelsListCompanyChannelsResponse = OrdersApisCompanyChannelsListCompanyChannelsResponses[keyof OrdersApisCompanyChannelsListCompanyChannelsResponses];

export type OrdersApisCompanyChannelsCreateCompanyChannelData = {
    body: CreateCompanyChannelSchema;
    path?: never;
    query?: never;
    url: '/api/orders/company-channels/';
};

export type OrdersApisCompanyChannelsCreateCompanyChannelResponses = {
    /**
     * OK
     */
    200: CompanyChannelSchema;
};

export type OrdersApisCompanyChannelsCreateCompanyChannelResponse = OrdersApisCompanyChannelsCreateCompanyChannelResponses[keyof OrdersApisCompanyChannelsCreateCompanyChannelResponses];

export type OrdersApisCompanyChannelsDeleteCompanyChannelData = {
    body?: never;
    path: {
        /**
         * Channel Id
         */
        channel_id: string;
    };
    query?: never;
    url: '/api/orders/company-channels/{channel_id}';
};

export type OrdersApisCompanyChannelsDeleteCompanyChannelResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type OrdersApisCompanyChannelsDeleteCompanyChannelResponse = OrdersApisCompanyChannelsDeleteCompanyChannelResponses[keyof OrdersApisCompanyChannelsDeleteCompanyChannelResponses];

export type OrdersApisCompanyChannelsGetCompanyChannelData = {
    body?: never;
    path: {
        /**
         * Channel Id
         */
        channel_id: string;
    };
    query?: never;
    url: '/api/orders/company-channels/{channel_id}';
};

export type OrdersApisCompanyChannelsGetCompanyChannelResponses = {
    /**
     * OK
     */
    200: CompanyChannelSchema;
};

export type OrdersApisCompanyChannelsGetCompanyChannelResponse = OrdersApisCompanyChannelsGetCompanyChannelResponses[keyof OrdersApisCompanyChannelsGetCompanyChannelResponses];

export type OrdersApisCompanyChannelsUpdateCompanyChannelData = {
    body: UpdateCompanyChannelSchema;
    path: {
        /**
         * Channel Id
         */
        channel_id: string;
    };
    query?: never;
    url: '/api/orders/company-channels/{channel_id}';
};

export type OrdersApisCompanyChannelsUpdateCompanyChannelResponses = {
    /**
     * OK
     */
    200: CompanyChannelSchema;
};

export type OrdersApisCompanyChannelsUpdateCompanyChannelResponse = OrdersApisCompanyChannelsUpdateCompanyChannelResponses[keyof OrdersApisCompanyChannelsUpdateCompanyChannelResponses];

export type OrdersApisCancellationTemplatesListCancellationTemplatesData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Office Id
         */
        office_id?: string | null;
    };
    url: '/api/orders/cancellation-templates/';
};

export type OrdersApisCancellationTemplatesListCancellationTemplatesResponses = {
    /**
     * OK
     */
    200: OrderStatusTemplateListResponseSchema;
};

export type OrdersApisCancellationTemplatesListCancellationTemplatesResponse = OrdersApisCancellationTemplatesListCancellationTemplatesResponses[keyof OrdersApisCancellationTemplatesListCancellationTemplatesResponses];

export type OrdersApisCancellationTemplatesCreateCancellationTemplateData = {
    body: CreateOrderStatusTemplateSchema;
    path?: never;
    query?: never;
    url: '/api/orders/cancellation-templates/';
};

export type OrdersApisCancellationTemplatesCreateCancellationTemplateResponses = {
    /**
     * OK
     */
    200: OrderStatusTemplateSchema;
};

export type OrdersApisCancellationTemplatesCreateCancellationTemplateResponse = OrdersApisCancellationTemplatesCreateCancellationTemplateResponses[keyof OrdersApisCancellationTemplatesCreateCancellationTemplateResponses];

export type OrdersApisCancellationTemplatesDeleteCancellationTemplateData = {
    body?: never;
    path: {
        /**
         * Template Id
         */
        template_id: string;
    };
    query?: never;
    url: '/api/orders/cancellation-templates/{template_id}';
};

export type OrdersApisCancellationTemplatesDeleteCancellationTemplateResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type OrdersApisCancellationTemplatesDeleteCancellationTemplateResponse = OrdersApisCancellationTemplatesDeleteCancellationTemplateResponses[keyof OrdersApisCancellationTemplatesDeleteCancellationTemplateResponses];

export type OrdersApisCancellationTemplatesGetCancellationTemplateData = {
    body?: never;
    path: {
        /**
         * Template Id
         */
        template_id: string;
    };
    query?: never;
    url: '/api/orders/cancellation-templates/{template_id}';
};

export type OrdersApisCancellationTemplatesGetCancellationTemplateResponses = {
    /**
     * OK
     */
    200: OrderStatusTemplateSchema;
};

export type OrdersApisCancellationTemplatesGetCancellationTemplateResponse = OrdersApisCancellationTemplatesGetCancellationTemplateResponses[keyof OrdersApisCancellationTemplatesGetCancellationTemplateResponses];

export type OrdersApisCancellationTemplatesUpdateCancellationTemplateData = {
    body: UpdateOrderStatusTemplateSchema;
    path: {
        /**
         * Template Id
         */
        template_id: string;
    };
    query?: never;
    url: '/api/orders/cancellation-templates/{template_id}';
};

export type OrdersApisCancellationTemplatesUpdateCancellationTemplateResponses = {
    /**
     * OK
     */
    200: OrderStatusTemplateSchema;
};

export type OrdersApisCancellationTemplatesUpdateCancellationTemplateResponse = OrdersApisCancellationTemplatesUpdateCancellationTemplateResponses[keyof OrdersApisCancellationTemplatesUpdateCancellationTemplateResponses];

export type OrdersApisOrdersListOrdersData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Office Id
         */
        office_id?: string | null;
        /**
         * Status
         */
        status?: string | null;
        /**
         * Assigned To Id
         */
        assigned_to_id?: string | null;
        /**
         * Customer Company Id
         */
        customer_company_id?: string | null;
        /**
         * Search
         */
        search?: string | null;
        /**
         * Created At From
         */
        created_at_from?: string | null;
        /**
         * Created At To
         */
        created_at_to?: string | null;
        /**
         * Assigned To Isnull
         */
        assigned_to_isnull?: boolean;
    };
    url: '/api/orders/';
};

export type OrdersApisOrdersListOrdersResponses = {
    /**
     * OK
     */
    200: OrderListResponseSchema;
};

export type OrdersApisOrdersListOrdersResponse = OrdersApisOrdersListOrdersResponses[keyof OrdersApisOrdersListOrdersResponses];

export type OrdersApisOrdersCreateOrderData = {
    body: CreateOrderSchema;
    path?: never;
    query?: never;
    url: '/api/orders/';
};

export type OrdersApisOrdersCreateOrderResponses = {
    /**
     * OK
     */
    200: OrderSchema;
};

export type OrdersApisOrdersCreateOrderResponse = OrdersApisOrdersCreateOrderResponses[keyof OrdersApisOrdersCreateOrderResponses];

export type OrdersApisOrdersCreateOrdersBulkData = {
    /**
     * Data
     */
    body: Array<CreateOrderSchema>;
    path?: never;
    query?: never;
    url: '/api/orders/bulk';
};

export type OrdersApisOrdersCreateOrdersBulkResponses = {
    /**
     * OK
     */
    200: BulkCreateOrderResponseSchema;
};

export type OrdersApisOrdersCreateOrdersBulkResponse = OrdersApisOrdersCreateOrdersBulkResponses[keyof OrdersApisOrdersCreateOrdersBulkResponses];

export type OrdersApisOrdersDeleteOrderData = {
    body?: never;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: never;
    url: '/api/orders/{order_id}';
};

export type OrdersApisOrdersDeleteOrderResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type OrdersApisOrdersGetOrderData = {
    body?: never;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: never;
    url: '/api/orders/{order_id}';
};

export type OrdersApisOrdersGetOrderResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type OrdersApisOrdersUpdateOrderData = {
    body: UpdateOrderSchema;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: never;
    url: '/api/orders/{order_id}';
};

export type OrdersApisOrdersUpdateOrderResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type OrdersApisSheetsListSheetsData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Office Id
         */
        office_id?: string | null;
        /**
         * Active Only
         */
        active_only?: boolean;
    };
    url: '/api/orders/sheets/';
};

export type OrdersApisSheetsListSheetsResponses = {
    /**
     * OK
     */
    200: OrdersSheetListResponseSchema;
};

export type OrdersApisSheetsListSheetsResponse = OrdersApisSheetsListSheetsResponses[keyof OrdersApisSheetsListSheetsResponses];

export type OrdersApisSheetsCreateSheetData = {
    body: CreateOrdersSheetSchema;
    path?: never;
    query?: never;
    url: '/api/orders/sheets/';
};

export type OrdersApisSheetsCreateSheetResponses = {
    /**
     * OK
     */
    200: OrdersSheetSchema;
};

export type OrdersApisSheetsCreateSheetResponse = OrdersApisSheetsCreateSheetResponses[keyof OrdersApisSheetsCreateSheetResponses];

export type OrdersApisSheetsListActiveSheetsData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Office Id
         */
        office_id?: string | null;
    };
    url: '/api/orders/sheets/active/';
};

export type OrdersApisSheetsListActiveSheetsResponses = {
    /**
     * Response
     * OK
     */
    200: Array<OrdersSheetSchema>;
};

export type OrdersApisSheetsListActiveSheetsResponse = OrdersApisSheetsListActiveSheetsResponses[keyof OrdersApisSheetsListActiveSheetsResponses];

export type OrdersApisSheetsDeleteSheetData = {
    body?: never;
    path: {
        /**
         * Sheet Id
         */
        sheet_id: string;
    };
    query?: never;
    url: '/api/orders/sheets/{sheet_id}';
};

export type OrdersApisSheetsDeleteSheetResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type OrdersApisSheetsGetSheetData = {
    body?: never;
    path: {
        /**
         * Sheet Id
         */
        sheet_id: string;
    };
    query?: never;
    url: '/api/orders/sheets/{sheet_id}';
};

export type OrdersApisSheetsGetSheetResponses = {
    /**
     * OK
     */
    200: OrdersSheetSchema;
};

export type OrdersApisSheetsGetSheetResponse = OrdersApisSheetsGetSheetResponses[keyof OrdersApisSheetsGetSheetResponses];

export type OrdersApisSheetsUpdateSheetData = {
    body: UpdateOrdersSheetSchema;
    path: {
        /**
         * Sheet Id
         */
        sheet_id: string;
    };
    query?: never;
    url: '/api/orders/sheets/{sheet_id}';
};

export type OrdersApisSheetsUpdateSheetResponses = {
    /**
     * OK
     */
    200: OrdersSheetSchema;
};

export type OrdersApisSheetsUpdateSheetResponse = OrdersApisSheetsUpdateSheetResponses[keyof OrdersApisSheetsUpdateSheetResponses];

export type OrdersApisDashboardGetDashboardStatsData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Sheet Id
         */
        sheet_id?: string | null;
        /**
         * Office Id
         */
        office_id?: string | null;
    };
    url: '/api/orders/dashboard/stats/';
};

export type OrdersApisDashboardGetDashboardStatsResponses = {
    /**
     * OK
     */
    200: DashboardStatsSchema;
};

export type OrdersApisDashboardGetDashboardStatsResponse = OrdersApisDashboardGetDashboardStatsResponses[keyof OrdersApisDashboardGetDashboardStatsResponses];

export type OrdersApisDashboardGetDashboardEmployeesData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Sheet Id
         */
        sheet_id?: string | null;
        /**
         * Office Id
         */
        office_id?: string | null;
    };
    url: '/api/orders/dashboard/employees/';
};

export type OrdersApisDashboardGetDashboardEmployeesResponses = {
    /**
     * OK
     */
    200: DashboardEmployeesSchema;
};

export type OrdersApisDashboardGetDashboardEmployeesResponse = OrdersApisDashboardGetDashboardEmployeesResponses[keyof OrdersApisDashboardGetDashboardEmployeesResponses];

export type OrdersApisDashboardGetDashboardChartsData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Office Id
         */
        office_id?: string | null;
    };
    url: '/api/orders/dashboard/charts/';
};

export type OrdersApisDashboardGetDashboardChartsResponses = {
    /**
     * OK
     */
    200: DashboardChartsSchema;
};

export type OrdersApisDashboardGetDashboardChartsResponse = OrdersApisDashboardGetDashboardChartsResponses[keyof OrdersApisDashboardGetDashboardChartsResponses];

export type OrdersApisStatusHistoryListOrderStatusHistoryData = {
    body?: never;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
    };
    url: '/api/orders/{order_id}/status-history/';
};

export type OrdersApisStatusHistoryListOrderStatusHistoryResponses = {
    /**
     * OK
     */
    200: OrderStatusHistoryListResponseSchema;
};

export type OrdersApisStatusHistoryListOrderStatusHistoryResponse = OrdersApisStatusHistoryListOrderStatusHistoryResponses[keyof OrdersApisStatusHistoryListOrderStatusHistoryResponses];

export type OrdersApisAssigneeHistoryListOrderAssigneeHistoryData = {
    body?: never;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
    };
    url: '/api/orders/{order_id}/assignee-history/';
};

export type OrdersApisAssigneeHistoryListOrderAssigneeHistoryResponses = {
    /**
     * OK
     */
    200: OrderAssigneeHistoryListResponseSchema;
};

export type OrdersApisAssigneeHistoryListOrderAssigneeHistoryResponse = OrdersApisAssigneeHistoryListOrderAssigneeHistoryResponses[keyof OrdersApisAssigneeHistoryListOrderAssigneeHistoryResponses];

export type ClientOptions = {
    baseUrl: string;
};