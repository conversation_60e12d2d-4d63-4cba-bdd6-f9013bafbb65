// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from './client';
import type { AccountsApisLoginData, AccountsApisLoginResponses, AccountsApisLogoutData, AccountsApisLogoutResponses, AccountsApisGetMeData, AccountsApisGetMeResponses, AccountsApisListUsersData, AccountsApisListUsersResponses, AccountsApisCreateUserData, AccountsApisCreateUserResponses, AccountsApisDeleteUserData, AccountsApisDeleteUserResponses, AccountsApisGetUserData, AccountsApisGetUserResponses, AccountsApisUpdateUserData, AccountsApisUpdateUserResponses, AccountsApisUpdateLocationData, AccountsApisUpdateLocationResponses, AccountsApisGetLocationHistoryData, AccountsApisGetLocationHistoryResponses, AccountsApisGetMyOfficesData, AccountsApisGetMyOfficesResponses, AccountsApisGetMeWalletData, AccountsApisGetMeWalletResponses, AccountsApisUpdateMeWalletData, AccountsApisUpdateMeWalletResponses, AccountsApisGetMePointsData, AccountsApisGetMePointsResponses, AccountsApisAddPointsData, AccountsApisAddPointsResponses, MerchantsApisListOfficesData, MerchantsApisListOfficesResponses, MerchantsApisCreateOfficeData, MerchantsApisCreateOfficeResponses, MerchantsApisDeleteOfficeData, MerchantsApisDeleteOfficeResponses, MerchantsApisGetOfficeData, MerchantsApisGetOfficeResponses, MerchantsApisUpdateOfficeData, MerchantsApisUpdateOfficeResponses, MerchantsApisListEmployeesOfficesByPhoneNumberData, MerchantsApisListEmployeesOfficesByPhoneNumberResponses, MerchantsApisListOfficeEmployeesData, MerchantsApisListOfficeEmployeesResponses, MerchantsApisAddOfficeEmployeeData, MerchantsApisAddOfficeEmployeeResponses, MerchantsApisRemoveOfficeEmployeeData, MerchantsApisRemoveOfficeEmployeeResponses, OrdersApisCompaniesListCompaniesData, OrdersApisCompaniesListCompaniesResponses, OrdersApisCompaniesCreateCompanyData, OrdersApisCompaniesCreateCompanyResponses, OrdersApisCompaniesDeleteCompanyData, OrdersApisCompaniesDeleteCompanyResponses, OrdersApisCompaniesGetCompanyData, OrdersApisCompaniesGetCompanyResponses, OrdersApisCompaniesUpdateCompanyData, OrdersApisCompaniesUpdateCompanyResponses, OrdersApisCompanyChannelsListCompanyChannelsData, OrdersApisCompanyChannelsListCompanyChannelsResponses, OrdersApisCompanyChannelsCreateCompanyChannelData, OrdersApisCompanyChannelsCreateCompanyChannelResponses, OrdersApisCompanyChannelsDeleteCompanyChannelData, OrdersApisCompanyChannelsDeleteCompanyChannelResponses, OrdersApisCompanyChannelsGetCompanyChannelData, OrdersApisCompanyChannelsGetCompanyChannelResponses, OrdersApisCompanyChannelsUpdateCompanyChannelData, OrdersApisCompanyChannelsUpdateCompanyChannelResponses, OrdersApisCancellationTemplatesListCancellationTemplatesData, OrdersApisCancellationTemplatesListCancellationTemplatesResponses, OrdersApisCancellationTemplatesCreateCancellationTemplateData, OrdersApisCancellationTemplatesCreateCancellationTemplateResponses, OrdersApisCancellationTemplatesDeleteCancellationTemplateData, OrdersApisCancellationTemplatesDeleteCancellationTemplateResponses, OrdersApisCancellationTemplatesGetCancellationTemplateData, OrdersApisCancellationTemplatesGetCancellationTemplateResponses, OrdersApisCancellationTemplatesUpdateCancellationTemplateData, OrdersApisCancellationTemplatesUpdateCancellationTemplateResponses, OrdersApisOrdersListOrdersData, OrdersApisOrdersListOrdersResponses, OrdersApisOrdersCreateOrderData, OrdersApisOrdersCreateOrderResponses, OrdersApisOrdersCreateOrdersBulkData, OrdersApisOrdersCreateOrdersBulkResponses, OrdersApisOrdersDeleteOrderData, OrdersApisOrdersDeleteOrderResponses, OrdersApisOrdersGetOrderData, OrdersApisOrdersGetOrderResponses, OrdersApisOrdersUpdateOrderData, OrdersApisOrdersUpdateOrderResponses, OrdersApisSheetsListSheetsData, OrdersApisSheetsListSheetsResponses, OrdersApisSheetsCreateSheetData, OrdersApisSheetsCreateSheetResponses, OrdersApisSheetsListActiveSheetsData, OrdersApisSheetsListActiveSheetsResponses, OrdersApisSheetsDeleteSheetData, OrdersApisSheetsDeleteSheetResponses, OrdersApisSheetsGetSheetData, OrdersApisSheetsGetSheetResponses, OrdersApisSheetsUpdateSheetData, OrdersApisSheetsUpdateSheetResponses, OrdersApisDashboardGetDashboardStatsData, OrdersApisDashboardGetDashboardStatsResponses, OrdersApisDashboardGetDashboardEmployeesData, OrdersApisDashboardGetDashboardEmployeesResponses, OrdersApisDashboardGetDashboardChartsData, OrdersApisDashboardGetDashboardChartsResponses, OrdersApisStatusHistoryListOrderStatusHistoryData, OrdersApisStatusHistoryListOrderStatusHistoryResponses, OrdersApisAssigneeHistoryListOrderAssigneeHistoryData, OrdersApisAssigneeHistoryListOrderAssigneeHistoryResponses } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Login
 */
export const accountsApisLogin = <ThrowOnError extends boolean = false>(options: Options<AccountsApisLoginData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AccountsApisLoginResponses, unknown, ThrowOnError>({
        url: '/api/accounts/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Logout
 */
export const accountsApisLogout = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<AccountsApisLogoutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/logout',
        ...options
    });
};

/**
 * Get Me
 */
export const accountsApisGetMe = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetMeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetMeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me',
        ...options
    });
};

/**
 * List Users
 */
export const accountsApisListUsers = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisListUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisListUsersResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/',
        ...options
    });
};

/**
 * Create User
 */
export const accountsApisCreateUser = <ThrowOnError extends boolean = false>(options: Options<AccountsApisCreateUserData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AccountsApisCreateUserResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete User
 */
export const accountsApisDeleteUser = <ThrowOnError extends boolean = false>(options: Options<AccountsApisDeleteUserData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<AccountsApisDeleteUserResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/{user_id}',
        ...options
    });
};

/**
 * Get User
 */
export const accountsApisGetUser = <ThrowOnError extends boolean = false>(options: Options<AccountsApisGetUserData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<AccountsApisGetUserResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/{user_id}',
        ...options
    });
};

/**
 * Update User
 */
export const accountsApisUpdateUser = <ThrowOnError extends boolean = false>(options: Options<AccountsApisUpdateUserData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<AccountsApisUpdateUserResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/{user_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Location
 */
export const accountsApisUpdateLocation = <ThrowOnError extends boolean = false>(options: Options<AccountsApisUpdateLocationData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AccountsApisUpdateLocationResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/location',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Location History
 */
export const accountsApisGetLocationHistory = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetLocationHistoryData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetLocationHistoryResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/location/history',
        ...options
    });
};

/**
 * Get My Offices
 * Get all offices where the current user is an employee
 */
export const accountsApisGetMyOffices = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetMyOfficesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetMyOfficesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/offices',
        ...options
    });
};

/**
 * Get Me Wallet
 */
export const accountsApisGetMeWallet = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetMeWalletData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetMeWalletResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/wallet',
        ...options
    });
};

/**
 * Update Me Wallet
 */
export const accountsApisUpdateMeWallet = <ThrowOnError extends boolean = false>(options: Options<AccountsApisUpdateMeWalletData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<AccountsApisUpdateMeWalletResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/wallet',
        ...options
    });
};

/**
 * Get Me Points
 */
export const accountsApisGetMePoints = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetMePointsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetMePointsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/points',
        ...options
    });
};

/**
 * Add Points
 */
export const accountsApisAddPoints = <ThrowOnError extends boolean = false>(options: Options<AccountsApisAddPointsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AccountsApisAddPointsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/points',
        ...options
    });
};

/**
 * List Offices
 */
export const merchantsApisListOffices = <ThrowOnError extends boolean = false>(options?: Options<MerchantsApisListOfficesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<MerchantsApisListOfficesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/',
        ...options
    });
};

/**
 * Create Office
 */
export const merchantsApisCreateOffice = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisCreateOfficeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<MerchantsApisCreateOfficeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Office
 */
export const merchantsApisDeleteOffice = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisDeleteOfficeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<MerchantsApisDeleteOfficeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}',
        ...options
    });
};

/**
 * Get Office
 */
export const merchantsApisGetOffice = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisGetOfficeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<MerchantsApisGetOfficeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}',
        ...options
    });
};

/**
 * Update Office
 */
export const merchantsApisUpdateOffice = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisUpdateOfficeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<MerchantsApisUpdateOfficeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Employees Offices By Phone Number
 */
export const merchantsApisListEmployeesOfficesByPhoneNumber = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisListEmployeesOfficesByPhoneNumberData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<MerchantsApisListEmployeesOfficesByPhoneNumberResponses, unknown, ThrowOnError>({
        url: '/api/merchants/offices/get-by-phone-number/',
        ...options
    });
};

/**
 * List Office Employees
 */
export const merchantsApisListOfficeEmployees = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisListOfficeEmployeesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<MerchantsApisListOfficeEmployeesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}/employees/',
        ...options
    });
};

/**
 * Add Office Employee
 */
export const merchantsApisAddOfficeEmployee = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisAddOfficeEmployeeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<MerchantsApisAddOfficeEmployeeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}/employees/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Remove Office Employee
 */
export const merchantsApisRemoveOfficeEmployee = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisRemoveOfficeEmployeeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<MerchantsApisRemoveOfficeEmployeeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}/employees/{employee_id}',
        ...options
    });
};

/**
 * List Companies
 */
export const ordersApisCompaniesListCompanies = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisCompaniesListCompaniesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisCompaniesListCompaniesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/',
        ...options
    });
};

/**
 * Create Company
 */
export const ordersApisCompaniesCreateCompany = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCompaniesCreateCompanyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisCompaniesCreateCompanyResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Company
 */
export const ordersApisCompaniesDeleteCompany = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCompaniesDeleteCompanyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<OrdersApisCompaniesDeleteCompanyResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/{company_id}',
        ...options
    });
};

/**
 * Get Company
 */
export const ordersApisCompaniesGetCompany = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCompaniesGetCompanyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisCompaniesGetCompanyResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/{company_id}',
        ...options
    });
};

/**
 * Update Company
 */
export const ordersApisCompaniesUpdateCompany = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCompaniesUpdateCompanyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<OrdersApisCompaniesUpdateCompanyResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/{company_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Company Channels
 */
export const ordersApisCompanyChannelsListCompanyChannels = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisCompanyChannelsListCompanyChannelsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisCompanyChannelsListCompanyChannelsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/',
        ...options
    });
};

/**
 * Create Company Channel
 */
export const ordersApisCompanyChannelsCreateCompanyChannel = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCompanyChannelsCreateCompanyChannelData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisCompanyChannelsCreateCompanyChannelResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Company Channel
 */
export const ordersApisCompanyChannelsDeleteCompanyChannel = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCompanyChannelsDeleteCompanyChannelData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<OrdersApisCompanyChannelsDeleteCompanyChannelResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/{channel_id}',
        ...options
    });
};

/**
 * Get Company Channel
 */
export const ordersApisCompanyChannelsGetCompanyChannel = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCompanyChannelsGetCompanyChannelData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisCompanyChannelsGetCompanyChannelResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/{channel_id}',
        ...options
    });
};

/**
 * Update Company Channel
 */
export const ordersApisCompanyChannelsUpdateCompanyChannel = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCompanyChannelsUpdateCompanyChannelData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<OrdersApisCompanyChannelsUpdateCompanyChannelResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/{channel_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Cancellation Templates
 */
export const ordersApisCancellationTemplatesListCancellationTemplates = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisCancellationTemplatesListCancellationTemplatesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisCancellationTemplatesListCancellationTemplatesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/',
        ...options
    });
};

/**
 * Create Cancellation Template
 */
export const ordersApisCancellationTemplatesCreateCancellationTemplate = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCancellationTemplatesCreateCancellationTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisCancellationTemplatesCreateCancellationTemplateResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Cancellation Template
 */
export const ordersApisCancellationTemplatesDeleteCancellationTemplate = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCancellationTemplatesDeleteCancellationTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<OrdersApisCancellationTemplatesDeleteCancellationTemplateResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/{template_id}',
        ...options
    });
};

/**
 * Get Cancellation Template
 */
export const ordersApisCancellationTemplatesGetCancellationTemplate = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCancellationTemplatesGetCancellationTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisCancellationTemplatesGetCancellationTemplateResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/{template_id}',
        ...options
    });
};

/**
 * Update Cancellation Template
 */
export const ordersApisCancellationTemplatesUpdateCancellationTemplate = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCancellationTemplatesUpdateCancellationTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<OrdersApisCancellationTemplatesUpdateCancellationTemplateResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/{template_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Orders
 */
export const ordersApisOrdersListOrders = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisOrdersListOrdersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisOrdersListOrdersResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/',
        ...options
    });
};

/**
 * Create Order
 */
export const ordersApisOrdersCreateOrder = <ThrowOnError extends boolean = false>(options: Options<OrdersApisOrdersCreateOrderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisOrdersCreateOrderResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Orders Bulk
 * Create multiple orders in bulk with transaction support and comprehensive error handling.
 *
 * This endpoint processes multiple orders atomically - either all orders are created successfully
 * or none are created if any validation fails. Each order is validated individually and
 * detailed error information is provided for failed orders.
 */
export const ordersApisOrdersCreateOrdersBulk = <ThrowOnError extends boolean = false>(options: Options<OrdersApisOrdersCreateOrdersBulkData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisOrdersCreateOrdersBulkResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Order
 */
export const ordersApisOrdersDeleteOrder = <ThrowOnError extends boolean = false>(options: Options<OrdersApisOrdersDeleteOrderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<OrdersApisOrdersDeleteOrderResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}',
        ...options
    });
};

/**
 * Get Order
 */
export const ordersApisOrdersGetOrder = <ThrowOnError extends boolean = false>(options: Options<OrdersApisOrdersGetOrderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisOrdersGetOrderResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}',
        ...options
    });
};

/**
 * Update Order
 */
export const ordersApisOrdersUpdateOrder = <ThrowOnError extends boolean = false>(options: Options<OrdersApisOrdersUpdateOrderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<OrdersApisOrdersUpdateOrderResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Sheets
 */
export const ordersApisSheetsListSheets = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisSheetsListSheetsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisSheetsListSheetsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/sheets/',
        ...options
    });
};

/**
 * Create Sheet
 */
export const ordersApisSheetsCreateSheet = <ThrowOnError extends boolean = false>(options: Options<OrdersApisSheetsCreateSheetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisSheetsCreateSheetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/sheets/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Active Sheets
 * Get active sheets for dropdown selection
 */
export const ordersApisSheetsListActiveSheets = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisSheetsListActiveSheetsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisSheetsListActiveSheetsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/sheets/active/',
        ...options
    });
};

/**
 * Delete Sheet
 */
export const ordersApisSheetsDeleteSheet = <ThrowOnError extends boolean = false>(options: Options<OrdersApisSheetsDeleteSheetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<OrdersApisSheetsDeleteSheetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/sheets/{sheet_id}',
        ...options
    });
};

/**
 * Get Sheet
 */
export const ordersApisSheetsGetSheet = <ThrowOnError extends boolean = false>(options: Options<OrdersApisSheetsGetSheetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisSheetsGetSheetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/sheets/{sheet_id}',
        ...options
    });
};

/**
 * Update Sheet
 */
export const ordersApisSheetsUpdateSheet = <ThrowOnError extends boolean = false>(options: Options<OrdersApisSheetsUpdateSheetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<OrdersApisSheetsUpdateSheetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/sheets/{sheet_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Dashboard Stats
 * Get dashboard statistics with optional sheet filtering
 */
export const ordersApisDashboardGetDashboardStats = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisDashboardGetDashboardStatsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisDashboardGetDashboardStatsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/dashboard/stats/',
        ...options
    });
};

/**
 * Get Dashboard Employees
 * Get employee performance metrics with optional sheet filtering
 */
export const ordersApisDashboardGetDashboardEmployees = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisDashboardGetDashboardEmployeesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisDashboardGetDashboardEmployeesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/dashboard/employees/',
        ...options
    });
};

/**
 * Get Dashboard Charts
 * Get revenue data grouped by sheet across different time periods (main tab only)
 */
export const ordersApisDashboardGetDashboardCharts = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisDashboardGetDashboardChartsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisDashboardGetDashboardChartsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/dashboard/charts/',
        ...options
    });
};

/**
 * List Order Status History
 */
export const ordersApisStatusHistoryListOrderStatusHistory = <ThrowOnError extends boolean = false>(options: Options<OrdersApisStatusHistoryListOrderStatusHistoryData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisStatusHistoryListOrderStatusHistoryResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}/status-history/',
        ...options
    });
};

/**
 * List Order Assignee History
 */
export const ordersApisAssigneeHistoryListOrderAssigneeHistory = <ThrowOnError extends boolean = false>(options: Options<OrdersApisAssigneeHistoryListOrderAssigneeHistoryData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisAssigneeHistoryListOrderAssigneeHistoryResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}/assignee-history/',
        ...options
    });
};