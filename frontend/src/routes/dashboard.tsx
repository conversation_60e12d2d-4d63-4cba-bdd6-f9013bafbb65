import { createFileRoute } from '@tanstack/react-router'
import { SiteHeader } from '@/components/site-header'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useEffect, useState } from 'react'
import { ordersApisDashboardGetDashboardStats, ordersApisDashboardGetDashboardEmployees, ordersApisDashboardGetDashboardCharts, ordersApisSheetsListActiveSheets } from '@/client'
import { type DashboardStatsSchema, type DashboardEmployeesSchema, type DashboardChartsSchema, type OrdersSheetSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import { IconTrendingUp, IconUsers, IconCash, IconReceipt } from '@tabler/icons-react'

export const Route = createFileRoute('/dashboard')({
    component: RouteComponent,
})

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const [activeSheets, setActiveSheets] = useState<OrdersSheetSchema[]>([])
    const [selectedTab, setSelectedTab] = useState<string>('all')
    const [stats, setStats] = useState<DashboardStatsSchema | null>(null)
    const [employees, setEmployees] = useState<DashboardEmployeesSchema | null>(null)
    const [charts, setCharts] = useState<DashboardChartsSchema | null>(null)
    const [loading, setLoading] = useState(true)

    const fetchActiveSheets = async () => {
        try {
            const response = await ordersApisSheetsListActiveSheets({
                query: {
                    office_id: selectedOffice?.id,
                }
            })

            if (response.data) {
                setActiveSheets(response.data)
            }
        } catch (error) {
            console.error('Error fetching active sheets:', error)
        }
    }

    const fetchDashboardData = async (sheetId?: string) => {
        try {
            setLoading(true)

            // Fetch stats
            const statsResponse = await ordersApisDashboardGetDashboardStats({
                query: {
                    office_id: selectedOffice?.id,
                    sheet_id: sheetId || null,
                }
            })

            // Fetch employees
            const employeesResponse = await ordersApisDashboardGetDashboardEmployees({
                query: {
                    office_id: selectedOffice?.id,
                    sheet_id: sheetId || null,
                }
            })

            // Fetch charts (only for main tab)
            let chartsResponse = null
            if (!sheetId) {
                chartsResponse = await ordersApisDashboardGetDashboardCharts({
                    query: {
                        office_id: selectedOffice?.id,
                    }
                })
            }

            if (statsResponse.data) {
                setStats(statsResponse.data)
            }

            if (employeesResponse.data) {
                setEmployees(employeesResponse.data)
            }

            if (chartsResponse?.data) {
                setCharts(chartsResponse.data)
            }

        } catch (error) {
            console.error('Error fetching dashboard data:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات لوحة التحكم')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchActiveSheets()
    }, [selectedOffice?.id])

    useEffect(() => {
        if (selectedTab === 'all') {
            fetchDashboardData()
        } else {
            fetchDashboardData(selectedTab)
        }
    }, [selectedTab, selectedOffice?.id])

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
        }).format(amount)
    }

    return <>
        <SiteHeader title="لوحة التحكم" />
        <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
                <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                    <Tabs dir='rtl' value={selectedTab} onValueChange={setSelectedTab} className="w-full">
                        <TabsList className="flex gap-2">
                            <TabsTrigger value="all">جميع الأوراق</TabsTrigger>
                            {activeSheets.map((sheet) => (
                                <TabsTrigger key={sheet.id} value={sheet.id}>
                                    {sheet.name}
                                </TabsTrigger>
                            ))}
                        </TabsList>

                        <TabsContent value="all" className="space-y-6">
                            <DashboardContent
                                stats={stats}
                                employees={employees}
                                charts={charts}
                                loading={loading}
                                formatCurrency={formatCurrency}
                                showCharts={true}
                            />
                        </TabsContent>

                        {activeSheets.map((sheet) => (
                            <TabsContent key={sheet.id} value={sheet.id} className="space-y-6">
                                <DashboardContent
                                    stats={stats}
                                    employees={employees}
                    charts={null}
                                    loading={loading}
                                    formatCurrency={formatCurrency}
                                    showCharts={false}
                                    sheetName={sheet.name}
                                />
                            </TabsContent>
                        ))}
                    </Tabs>
                </div>
            </div>
        </div>
    </>
}

interface DashboardContentProps {
    stats: DashboardStatsSchema | null
    employees: DashboardEmployeesSchema | null
    charts: DashboardChartsSchema | null
    loading: boolean
    formatCurrency: (amount: number) => string
    showCharts: boolean
    sheetName?: string
}

function DashboardContent({ stats, employees, charts, loading, formatCurrency, showCharts, sheetName }: DashboardContentProps) {
    if (loading) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[400px]">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p className="mt-2 text-sm text-muted-foreground">جاري التحميل...</p>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            {sheetName && (
                <div className="px-4 lg:px-6">
                    <h2 className="text-2xl font-bold">إحصائيات ورقة: {sheetName}</h2>
                </div>
            )}

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 px-4 lg:px-6">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">إجمالي الطلبات</CardTitle>
                        <IconReceipt className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats?.total_orders || 0}</div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">إجمالي المبلغ</CardTitle>
                        <IconTrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatCurrency(stats?.total_amount || 0)}</div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">النقد المحصل</CardTitle>
                        <IconCash className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatCurrency(stats?.collected_cash || 0)}</div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">إجمالي العمولة</CardTitle>
                        <IconUsers className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatCurrency(stats?.total_commission || 0)}</div>
                    </CardContent>
                </Card>
            </div>

            {/* Charts - Only show in main tab */}
            {showCharts && charts && (
                <div className="px-4 lg:px-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>الإيرادات حسب الورقة</CardTitle>
                            <CardDescription>مقارنة الإيرادات عبر فترات زمنية مختلفة</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ChartComponent chartData={charts.chart_data} />
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Employee Table */}
            <div className="px-4 lg:px-6">
                <Card>
                    <CardHeader>
                        <CardTitle>أداء الموظفين</CardTitle>
                        <CardDescription>إحصائيات أداء الموظفين {sheetName ? `في ورقة ${sheetName}` : 'في جميع الأوراق'}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <EmployeeTable employees={employees?.employees || []} formatCurrency={formatCurrency} />
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}

interface EmployeeTableProps {
    employees: any[]
    formatCurrency: (amount: number) => string
}

function EmployeeTable({ employees, formatCurrency }: EmployeeTableProps) {
    if (!employees || employees.length === 0) {
        return (
            <div className="text-center py-8 text-muted-foreground">
                لا توجد بيانات موظفين متاحة
            </div>
        )
    }

    return (
        <div className="overflow-x-auto">
            <table className="w-full border-collapse">
                <thead>
                    <tr className="border-b">
                        <th className="text-right p-2 font-medium">اسم الموظف</th>
                        <th className="text-right p-2 font-medium">عدد الطلبات</th>
                        <th className="text-right p-2 font-medium">الطلبات المكتملة</th>
                        <th className="text-right p-2 font-medium">الطلبات المسلمة</th>
                        <th className="text-right p-2 font-medium">الطلبات الملغية</th>
                        <th className="text-right p-2 font-medium">رصيد المحفظة</th>
                        <th className="text-right p-2 font-medium">إجمالي المدفوعات</th>
                    </tr>
                </thead>
                <tbody>
                    {employees.map((employee) => (
                        <tr key={employee.id} className="border-b hover:bg-muted/50">
                            <td className="p-2 font-medium">{employee.name}</td>
                            <td className="p-2">{employee.orders_count}</td>
                            <td className="p-2">
                                <Badge variant="secondary">
                                    {employee.completed_orders_count}
                                </Badge>
                            </td>
                            <td className="p-2">
                                <Badge variant="default">
                                    {employee.delivered_orders_count}
                                </Badge>
                            </td>
                            <td className="p-2">
                                <Badge variant="destructive">
                                    {employee.cancelled_orders_count}
                                </Badge>
                            </td>
                            <td className="p-2">{formatCurrency(employee.current_wallet_balance)}</td>
                            <td className="p-2">{formatCurrency(employee.total_customer_payments)}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    )
}

interface ChartComponentProps {
    chartData: any[]
}

function ChartComponent({ chartData }: ChartComponentProps) {
    if (!chartData || chartData.length === 0) {
        return (
            <div className="text-center py-8 text-muted-foreground">
                لا توجد بيانات رسوم بيانية متاحة
            </div>
        )
    }

    // Group data by period for better visualization
    const periods = ['7_days', '1_month', '3_months']
    const periodLabels = {
        '7_days': '7 أيام',
        '1_month': 'شهر واحد',
        '3_months': '3 أشهر'
    }

    return (
        <div className="space-y-4">
            {periods.map((period) => {
                const periodData = chartData.filter(item => item.period === period)

                return (
                    <div key={period} className="space-y-2">
                        <h4 className="font-medium">{periodLabels[period as keyof typeof periodLabels]}</h4>
                        <div className="grid gap-2">
                            {periodData.map((item) => (
                                <div key={`${item.sheet_name}-${period}`} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                                    <span className="font-medium">{item.sheet_name}</span>
                                    <span className="text-sm text-muted-foreground">
                                        {new Intl.NumberFormat('ar-SA', {
                                            style: 'currency',
                                            currency: 'SAR',
                                        }).format(item.revenue)}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>
                )
            })}
        </div>
    )
}
