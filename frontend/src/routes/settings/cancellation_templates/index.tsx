import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { IconPlus, IconEdit, IconTrash, IconEye } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DataTableComponent } from '@/components/DataTableComponent'
import { type ColumnDef } from '@tanstack/react-table'
import { ordersApisListCancellationTemplates, ordersApisCreateCancellationTemplate, ordersApisUpdateCancellationTemplate, ordersApisDeleteCancellationTemplate } from '@/client'
import { type OrderStatusTemplateSchema, type CreateOrderStatusTemplateSchema, type UpdateOrderStatusTemplateSchema, type OrderHandlingStatusSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

export const Route = createFileRoute('/settings/cancellation_templates/')({
    component: RouteComponent,
})

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const navigate = useNavigate()
    const [templates, setTemplates] = useState<OrderStatusTemplateSchema[]>([])
    const [loading, setLoading] = useState(false)
    const [showForm, setShowForm] = useState(false)
    const [editingTemplate, setEditingTemplate] = useState<OrderStatusTemplateSchema | null>(null)
    const [formData, setFormData] = useState<Partial<CreateOrderStatusTemplateSchema>>({
        name: '',
        reason_template_text: '',
        order_default_handling_status: 'CANCELLED' as OrderHandlingStatusSchema,
        just_delivery_commission_rate: true,
        commission_fixed_rate: null,
        percentage_of_order_total_price: null,
    })

    useEffect(() => {
        fetchTemplates()
    }, [selectedOffice])

    const fetchTemplates = async () => {
        if (!selectedOffice) return

        try {
            setLoading(true)
            const response = await ordersApisListCancellationTemplates({
                query: {
                    office_id: selectedOffice.id,
                    page_size: 100,
                },
            })
            if (response.data) {
                setTemplates(response.data.templates)
            }
        } catch (error) {
            console.error('Error fetching templates:', error)
            toast.error('حدث خطأ أثناء تحميل قوالب الإلغاء')
        } finally {
            setLoading(false)
        }
    }

    const handleCreateTemplate = async () => {
        if (!selectedOffice) {
            toast.error('يرجى اختيار مكتب أولاً')
            return
        }

        if (!formData.name?.trim()) {
            toast.error('يرجى إدخال اسم القالب')
            return
        }

        try {
            setLoading(true)
            await ordersApisCreateCancellationTemplate({
                body: {
                    office_id: selectedOffice.id,
                    name: formData.name.trim(),
                    reason_template_text: formData.reason_template_text || '',
                    order_default_handling_status: formData.order_default_handling_status || 'CANCELLED',
                    just_delivery_commission_rate: formData.just_delivery_commission_rate || true,
                    commission_fixed_rate: formData.commission_fixed_rate ?? null,
                    percentage_of_order_total_price: formData.percentage_of_order_total_price ?? null,
                },
            })

            toast.success('تم إنشاء قالب الإلغاء بنجاح')
            setShowForm(false)
            resetForm()
            fetchTemplates()
        } catch (error) {
            console.error('Error creating template:', error)
            toast.error('حدث خطأ أثناء إنشاء قالب الإلغاء')
        } finally {
            setLoading(false)
        }
    }

    const handleUpdateTemplate = async () => {
        if (!editingTemplate) return

        if (!formData.name?.trim()) {
            toast.error('يرجى إدخال اسم القالب')
            return
        }

        try {
            setLoading(true)
            await ordersApisUpdateCancellationTemplate({
                path: { template_id: editingTemplate.id },
                body: {
                    name: formData.name.trim(),
                    reason_template_text: formData.reason_template_text || '',
                    order_default_handling_status: formData.order_default_handling_status || 'CANCELLED',
                    just_delivery_commission_rate: formData.just_delivery_commission_rate || true,
                    commission_fixed_rate: formData.commission_fixed_rate ?? null,
                    percentage_of_order_total_price: formData.percentage_of_order_total_price ?? null,
                },
            })

            toast.success('تم تحديث قالب الإلغاء بنجاح')
            setShowForm(false)
            setEditingTemplate(null)
            resetForm()
            fetchTemplates()
        } catch (error) {
            console.error('Error updating template:', error)
            toast.error('حدث خطأ أثناء تحديث قالب الإلغاء')
        } finally {
            setLoading(false)
        }
    }

    const handleDeleteTemplate = async (templateId: string) => {
        if (!confirm('هل أنت متأكد من حذف هذا القالب؟')) return

        try {
            setLoading(true)
            await ordersApisDeleteCancellationTemplate({
                path: { template_id: templateId },
            })

            toast.success('تم حذف قالب الإلغاء بنجاح')
            fetchTemplates()
        } catch (error) {
            console.error('Error deleting template:', error)
            toast.error('حدث خطأ أثناء حذف قالب الإلغاء')
        } finally {
            setLoading(false)
        }
    }

    const handleEditTemplate = (template: OrderStatusTemplateSchema) => {
        setEditingTemplate(template)
        setFormData({
            name: template.name,
            reason_template_text: template.reason_template_text,
            order_default_handling_status: template.order_default_handling_status,
            just_delivery_commission_rate: template.just_delivery_commission_rate,
            commission_fixed_rate: template.commission_fixed_rate,
            percentage_of_order_total_price: template.percentage_of_order_total_price,
        })
        setShowForm(true)
    }

    const resetForm = () => {
        setFormData({
            name: '',
            reason_template_text: '',
            order_default_handling_status: 'CANCELLED' as OrderHandlingStatusSchema,
            just_delivery_commission_rate: true,
            commission_fixed_rate: null,
            percentage_of_order_total_price: null,
        })
    }

    const handleFormClose = () => {
        setShowForm(false)
        setEditingTemplate(null)
        resetForm()
    }

    const columns: ColumnDef<OrderStatusTemplateSchema>[] = [
        {
            accessorKey: 'name',
            header: 'اسم القالب',
            cell: ({ row }) => (
                <div className="font-medium">{row.getValue('name')}</div>
            ),
        },
        {
            accessorKey: 'reason_template_text',
            header: 'نص القالب',
            cell: ({ row }) => (
                <div className="max-w-xs truncate" title={row.getValue('reason_template_text')}>
                    {row.getValue('reason_template_text') || '-'}
                </div>
            ),
        },
        {
            accessorKey: 'order_default_handling_status',
            header: 'حالة الطلب الافتراضية',
            cell: ({ row }) => {
                const status = row.getValue('order_default_handling_status') as OrderHandlingStatusSchema
                const statusMap = {
                    'PENDING': { label: 'في الانتظار', color: 'bg-yellow-100 text-yellow-800' },
                    'ASSIGNED': { label: 'مُسند', color: 'bg-blue-100 text-blue-800' },
                    'PROCESSING': { label: 'قيد المعالجة', color: 'bg-purple-100 text-purple-800' },
                    'CANCELLED': { label: 'ملغي', color: 'bg-red-100 text-red-800' },
                    'DELIVERED': { label: 'تم التوصيل', color: 'bg-green-100 text-green-800' },
                }
                const statusInfo = statusMap[status] || { label: status, color: 'bg-gray-100 text-gray-800' }
                return <Badge className={statusInfo.color}>{statusInfo.label}</Badge>
            },
        },
        {
            accessorKey: 'just_delivery_commission_rate',
            header: 'عمولة التوصيل فقط',
            cell: ({ row }) => (
                <div className="text-center">
                    {row.getValue('just_delivery_commission_rate') ? 'نعم' : 'لا'}
                </div>
            ),
        },
        {
            accessorKey: 'commission_fixed_rate',
            header: 'نسبة العمولة الثابتة',
            cell: ({ row }) => {
                const rate = row.getValue('commission_fixed_rate') as number | null
                return <div className="text-center">{rate ? `${rate}%` : '-'}</div>
            },
        },
        {
            accessorKey: 'percentage_of_order_total_price',
            header: 'نسبة من إجمالي السعر',
            cell: ({ row }) => {
                const percentage = row.getValue('percentage_of_order_total_price') as number | null
                return <div className="text-center">{percentage ? `${percentage}%` : '-'}</div>
            },
        },
        {
            id: 'actions',
            header: 'الإجراءات',
            cell: ({ row }) => {
                if (row.original.office_id === null) {
                    return null
                }
                return <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditTemplate(row.original)}
                    >
                        <IconEdit className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteTemplate(row.original.id)}
                        className="text-red-600 hover:text-red-700"
                    >
                        <IconTrash className="h-4 w-4" />
                    </Button>
                </div>
            }
        },
    ]

    if (!selectedOffice) {
        return (
            <div className="container mx-auto p-6">
                <div className="text-center text-gray-500">
                    يرجى اختيار مكتب أولاً للوصول إلى قوالب الإلغاء
                </div>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-6">
            <div className="mb-6">
                <h1 className="text-2xl font-bold mb-2">إدارة قوالب الإلغاء</h1>
                <p className="text-gray-600">
                    إدارة قوالب أسباب الإلغاء لمكتب {selectedOffice.name}
                </p>
            </div>

            <div className="mb-4 flex justify-between items-center">
                <div>
                    <p className="text-sm text-gray-500">
                        إجمالي القوالب: {templates.length}
                    </p>
                </div>
                <Button onClick={() => setShowForm(true)}>
                    <IconPlus className="h-4 w-4 mr-2" />
                    إضافة قالب جديد
                </Button>
            </div>

            <DataTableComponent
                data={templates}
                columns={columns}
                enablePagination={false}
                emptyMessage="لا توجد قوالب إلغاء"
                className="bg-white rounded-lg shadow"
            />

            {/* Create/Edit Form Dialog */}
            <Dialog open={showForm} onOpenChange={setShowForm}>
                <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>
                            {editingTemplate ? 'تعديل قالب الإلغاء' : 'إضافة قالب إلغاء جديد'}
                        </DialogTitle>
                        <DialogDescription>
                            {editingTemplate
                                ? 'قم بتعديل بيانات قالب الإلغاء'
                                : 'أضف قالب إلغاء جديد لمكتبك'
                            }
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="name">اسم القالب *</Label>
                            <Input
                                id="name"
                                value={formData.name || ''}
                                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                placeholder="أدخل اسم القالب"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="reason_template_text">نص القالب</Label>
                            <Textarea
                                id="reason_template_text"
                                value={formData.reason_template_text || ''}
                                onChange={(e) => setFormData({ ...formData, reason_template_text: e.target.value })}
                                placeholder="أدخل نص القالب"
                                rows={3}
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="handling_status">حالة الطلب الافتراضية</Label>
                            <Select
                                value={formData.order_default_handling_status || 'CANCELLED'}
                                onValueChange={(value) => setFormData({
                                    ...formData,
                                    order_default_handling_status: value as OrderHandlingStatusSchema
                                })}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="PENDING">في الانتظار</SelectItem>
                                    <SelectItem value="ASSIGNED">مُسند</SelectItem>
                                    <SelectItem value="PROCESSING">قيد المعالجة</SelectItem>
                                    <SelectItem value="CANCELLED">ملغي</SelectItem>
                                    <SelectItem value="DELIVERED">تم التوصيل</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <Separator />

                        <div className="space-y-4">
                            <div className="flex items-center space-x-2 space-y-2">
                                <Checkbox
                                    id="just_delivery"
                                    checked={formData.just_delivery_commission_rate || false}
                                    onCheckedChange={(checked) => setFormData({
                                        ...formData,
                                        just_delivery_commission_rate: checked as boolean
                                    })}
                                />
                                <Label htmlFor="just_delivery">عمولة التوصيل فقط</Label>
                            </div>

                            {!formData.just_delivery_commission_rate && (
                                <div className="grid grid-cols-2 gap-4 space-y-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="commission_fixed_rate"> العمولة الثابتة</Label>
                                        <Input
                                            id="commission_fixed_rate"
                                            type="number"
                                            min="0"
                                            max="100"
                                            step="0.01"
                                            value={formData.commission_fixed_rate || ''}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                commission_fixed_rate: e.target.value ? parseFloat(e.target.value) : null
                                            })}
                                            placeholder="0.00"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="percentage_of_order">نسبة من إجمالي السعر (%)</Label>
                                        <Input
                                            id="percentage_of_order"
                                            type="number"
                                            min="0"
                                            max="100"
                                            step="0.01"
                                            value={formData.percentage_of_order_total_price || ''}
                                            onChange={(e) => setFormData({
                                                ...formData,
                                                percentage_of_order_total_price: e.target.value ? parseFloat(e.target.value) : null
                                            })}
                                            placeholder="0.00"
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" onClick={handleFormClose}>
                            إلغاء
                        </Button>
                        <Button
                            onClick={editingTemplate ? handleUpdateTemplate : handleCreateTemplate}
                            disabled={loading || !formData.name?.trim()}
                        >
                            {loading ? 'جاري الحفظ...' : (editingTemplate ? 'تحديث' : 'إنشاء')}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
