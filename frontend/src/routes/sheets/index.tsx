import { SiteHeader } from '@/components/site-header'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { DataTableComponent } from '@/components/DataTableComponent'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useEffect, useState } from 'react'
import { ordersApisSheetsListSheets, ordersApisSheetsDeleteSheet } from '@/client'
import { type OrdersSheetSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import { IconPlus, IconEdit, IconTrash } from '@tabler/icons-react'

export const Route = createFileRoute('/sheets/')({
    component: RouteComponent,
})

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const navigate = useNavigate()
    const [data, setData] = useState<OrdersSheetSchema[]>([])
    const [loading, setLoading] = useState(true)

    const fetchSheets = async () => {
        try {
            setLoading(true)
            const response = await ordersApisSheetsListSheets({
                query: {
                    office_id: selectedOffice.id,
                    page_size: 100, // Get all sheets for now
                }
            })
            
            if (response.data) {
                setData(response.data.results)
            }
        } catch (error) {
            console.error('Error fetching sheets:', error)
            toast.error('حدث خطأ أثناء تحميل الأوراق')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchSheets()
    }, [selectedOffice.id])

    const handleDelete = async (sheetId: string) => {
        if (!confirm('هل أنت متأكد من حذف هذه الورقة؟ سيتم حذف جميع الطلبات المرتبطة بها.')) {
            return
        }

        try {
            await ordersApisSheetsDeleteSheet({
                path: { sheet_id: sheetId }
            })
            toast.success('تم حذف الورقة بنجاح')
            fetchSheets() // Refresh the list
        } catch (error) {
            console.error('Error deleting sheet:', error)
            toast.error('حدث خطأ أثناء حذف الورقة')
        }
    }

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        })
    }

    return <>
        <SiteHeader title="إدارة الأوراق" />
        <div className="flex flex-col m-4">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold">إدارة الأوراق</h1>
                    <p className="text-sm text-muted-foreground">هنا يمكنك إدارة أوراق الطلبات</p>
                </div>
            </div>
        </div>

        <div className="flex flex-col m-4">
            <DataTableComponent
                idField='id'
                pageSize={10}
                enablePagination={true}
                showAddButton={true}
                addButtonText="إضافة ورقة جديدة"
                onAddClick={() => {
                    navigate({
                        to: "/sheets/form",
                        search: { id: undefined }
                    })
                }}
                data={data}
                loading={loading}
                columns={[
                    {
                        header: "اسم الورقة",
                        accessorKey: "name",
                        cell: ({ row }) => (
                            <div className="flex flex-col">
                                <span className="font-medium">
                                    {row.original.name}
                                </span>
                                <span className="text-sm text-muted-foreground">
                                    {formatDate(row.original.created_at)}
                                </span>
                            </div>
                        ),
                    },
                    {
                        header: "الحالة",
                        accessorKey: "active",
                        cell: ({ row }) => (
                            <Badge variant={row.original.active ? "default" : "secondary"}>
                                {row.original.active ? "نشطة" : "غير نشطة"}
                            </Badge>
                        ),
                    },
                    {
                        header: "الملاحظات",
                        accessorKey: "notes",
                        cell: ({ row }) => (
                            <span className="text-sm text-muted-foreground max-w-xs truncate">
                                {row.original.notes || "لا توجد ملاحظات"}
                            </span>
                        ),
                    },
                    {
                        header: "الإجراءات",
                        id: "actions",
                        cell: ({ row }) => (
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        navigate({
                                            to: "/sheets/form",
                                            search: { id: row.original.id }
                                        })
                                    }}
                                    className="flex items-center gap-1"
                                >
                                    <IconEdit size={14} />
                                    تعديل
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDelete(row.original.id)}
                                    className="flex items-center gap-1 text-red-600 hover:text-red-700"
                                >
                                    <IconTrash size={14} />
                                    حذف
                                </Button>
                            </div>
                        ),
                    },
                ]}
            />
        </div>
    </>
}
