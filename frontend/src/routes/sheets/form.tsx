import { SiteHeader } from '@/components/site-header'
import { createFileRoute, useNavigate, useSearch } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { useEffect, useState } from 'react'
import { ordersApisSheetsGetSheet, ordersApisSheetsCreateSheet, ordersApisSheetsUpdateSheet } from '@/client'
import { type OrdersSheetSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import { Separator } from '@/components/ui/separator'

export const Route = createFileRoute('/sheets/form')({
    component: RouteComponent,
    validateSearch: (search: Record<string, unknown>) => ({
        id: search.id as string | undefined,
    }),
})

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const navigate = useNavigate()
    const { id } = useSearch({ from: '/sheets/form' })
    const [loading, setLoading] = useState(false)
    const [initialLoading, setInitialLoading] = useState(false)

    const [sheet, setSheet] = useState<Partial<OrdersSheetSchema>>({
        name: '',
        active: true,
        notes: '',
    })

    const isEditing = !!id

    useEffect(() => {
        if (isEditing && id) {
            fetchSheet(id)
        }
    }, [id, isEditing])

    const fetchSheet = async (sheetId: string) => {
        try {
            setInitialLoading(true)
            const response = await ordersApisSheetsGetSheet({
                path: { sheet_id: sheetId }
            })
            
            if (response.data) {
                setSheet(response.data)
            }
        } catch (error) {
            console.error('Error fetching sheet:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات الورقة')
            navigate({ to: '/sheets' })
        } finally {
            setInitialLoading(false)
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        
        if (!sheet.name?.trim()) {
            toast.error('يرجى إدخال اسم الورقة')
            return
        }

        try {
            setLoading(true)
            
            if (isEditing && id) {
                await ordersApisSheetsUpdateSheet({
                    path: { sheet_id: id },
                    body: {
                        name: sheet.name.trim(),
                        active: sheet.active!,
                        notes: sheet.notes || '',
                    },
                })
                toast.success('تم تحديث الورقة بنجاح')
            } else {
                await ordersApisSheetsCreateSheet({
                    body: {
                        office_id: selectedOffice.id,
                        name: sheet.name.trim(),
                        active: sheet.active,
                        notes: sheet.notes || '',
                    },
                })
                toast.success('تم إنشاء الورقة بنجاح')
            }

            navigate({ to: '/sheets' })
        } catch (error) {
            console.error('Error saving sheet:', error)
            toast.error('حدث خطأ أثناء حفظ الورقة')
        } finally {
            setLoading(false)
        }
    }

    const handleInputChange = (field: keyof OrdersSheetSchema, value: any) => {
        setSheet(prev => ({ ...prev, [field]: value }))
    }

    if (initialLoading) {
        return <>
            <SiteHeader title="إدارة الأوراق" />
            <div className="flex flex-col items-center justify-center min-h-[400px]">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p className="mt-2 text-sm text-muted-foreground">جاري التحميل...</p>
            </div>
        </>
    }

    return <>
        <SiteHeader title="إدارة الأوراق" />
        <div className="flex flex-col m-4">
            <h1 className="text-2xl font-bold">{isEditing ? "تعديل الورقة" : "إضافة ورقة جديدة"}</h1>
            <p className="text-sm text-muted-foreground">
                {isEditing ? "قم بتعديل بيانات الورقة" : "أدخل بيانات الورقة الجديدة"}
            </p>
        </div>
        <Separator className="mb-4" />

        <form onSubmit={handleSubmit} className="flex flex-col m-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">المعلومات الأساسية</h3>

                    <div className="space-y-2">
                        <Label htmlFor="name">اسم الورقة *</Label>
                        <Input
                            id="name"
                            value={sheet.name || ''}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                            required
                            disabled={loading}
                            placeholder="أدخل اسم الورقة"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="notes">الملاحظات</Label>
                        <Textarea
                            id="notes"
                            value={sheet.notes || ''}
                            onChange={(e) => handleInputChange('notes', e.target.value)}
                            disabled={loading}
                            placeholder="أدخل ملاحظات إضافية (اختياري)"
                            rows={4}
                        />
                    </div>
                </div>

                {/* Settings */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">الإعدادات</h3>

                    <div className="flex items-center space-x-2 space-x-reverse">
                        <Checkbox
                            id="active"
                            checked={sheet.active || false}
                            onCheckedChange={(checked) => handleInputChange('active', checked)}
                            disabled={loading}
                        />
                        <Label htmlFor="active" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            ورقة نشطة
                        </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                        الأوراق النشطة فقط ستظهر في قائمة اختيار الأوراق عند إنشاء الطلبات
                    </p>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 space-x-reverse">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate({ to: '/sheets' })}
                    disabled={loading}
                >
                    إلغاء
                </Button>
                <Button type="submit" disabled={loading}>
                    {loading ? 'جاري الحفظ...' : (isEditing ? 'تحديث الورقة' : 'إنشاء الورقة')}
                </Button>
            </div>
        </form>
    </>
}
