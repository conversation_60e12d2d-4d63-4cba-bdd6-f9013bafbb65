import { useState, useRef, useEffect } from "react";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  IconUpload,
  IconFileSpreadsheet,
  IconArrowLeft,
  IconFile,
  IconX,
  IconEye,
  IconArrowRight,
  IconLink,
  IconLoader2,
  IconCheck,
  IconAlertTriangle,
} from "@tabler/icons-react";
import * as XLSX from "xlsx";
import Papa from "papaparse";
import { toast } from "sonner";
import Spreadsheet from "react-spreadsheet";
import { ordersApisOrdersCreateOrdersBulk, ordersApisSheetsListActiveSheets } from "@/client";
import { useAuth } from "@/contexts/AuthContext";
import type {
  CreateOrderSchema,
  OrderHandlingStatusSchema,
  OrdersSheetSchema,
} from "@/client/types.gen";
import { SiteHeader } from "@/components/site-header";

export const Route = createFileRoute("/orders/bulk-import")({
  component: RouteComponent,
});

// Helper function to map row data to order (shared between components)
const mapRowToOrder = (
  row: any[],
  headers: string[],
  mapping: Record<string, string>,
  officeId: string,
  sheetId?: string | null
): CreateOrderSchema => {
  const orderData: Partial<CreateOrderSchema> = {
    office_id: officeId,
    handling_status: "PENDING" as OrderHandlingStatusSchema,
    notes: "",
    breakable: false,
    total_price: null,
    customer_company_id: null,
    deadline_date: null,
    commission_fixed_rate: null,
    assigned_to_id: null,
    final_customer_payment: null,
    status_template_id: null,
    status_reason: null,
    orders_sheet_id: sheetId || null,
    // Set required fields with defaults
    customer_name: "بدون اسم", // Default name if not provided
    customer_phone: "",
    // Set optional fields with proper defaults (matching backend schema)
    customer_address: "",
  };

  // Map each column to its corresponding order field
  headers.forEach((header, index) => {
    const fieldKey = mapping[header];
    if (!fieldKey || fieldKey === "" || fieldKey === "SKIP") return;

    const cellValue = row[index];

    // Special handling for customer_name - always process even if empty
    if (fieldKey === "customer_name") {
      const nameValue = cellValue ? String(cellValue).trim() : "";
      orderData[fieldKey] = nameValue || "بدون اسم";
      return;
    }

    // For other fields, skip if empty
    if (cellValue === null || cellValue === undefined || cellValue === "")
      return;

    switch (fieldKey) {
      case "customer_phone":
      case "customer_address":
      case "notes":
        orderData[fieldKey] = String(cellValue).trim();
        break;
      case "total_price":
      case "commission_fixed_rate":
      case "final_customer_payment":
        const numValue = parseFloat(String(cellValue));
        orderData[fieldKey] = isNaN(numValue) ? null : numValue;
        break;
      case "breakable":
        const boolValue = String(cellValue).toLowerCase();
        orderData[fieldKey] =
          boolValue === "true" || boolValue === "1" || boolValue === "نعم";
        break;
      case "deadline_date":
        // Try to parse date
        try {
          const dateValue = new Date(cellValue);
          if (!isNaN(dateValue.getTime())) {
            orderData[fieldKey] = dateValue.toISOString();
          }
        } catch {
          // Invalid date, leave as null
        }
        break;
    }
  });

  return orderData as CreateOrderSchema;
};

// Import workflow steps
enum ImportStep {
  UPLOAD = "upload",
  PREVIEW = "preview",
  MAPPING = "mapping",
  PROCESSING = "processing",
  RESULTS = "results",
}

interface ImportState {
  step: ImportStep;
  file: File | null;
  data: any[][];
  headers: string[];
  mapping: Record<string, string>;
  dataRange: {
    startRow: number;
    startCol: number;
    endRow: number;
    endCol: number;
  };
  results: {
    total: number;
    successful: number;
    failed: number;
    errors: Array<{ row: number; data: any; error: string }>;
  };
}

function RouteComponent() {
  const navigate = useNavigate();
  const { selectedOffice } = useAuth();

  // Sheet selection state
  const [selectedSheetId, setSelectedSheetId] = useState<string | null>(null);
  const [activeSheets, setActiveSheets] = useState<OrdersSheetSchema[]>([]);

  // Initialize state with a fresh state every time the component mounts
  const getInitialState = (): ImportState => ({
    step: ImportStep.UPLOAD,
    file: null,
    data: [],
    headers: [],
    mapping: {},
    dataRange: {
      startRow: 0,
      startCol: 0,
      endRow: 0,
      endCol: 0,
    },
    results: {
      total: 0,
      successful: 0,
      failed: 0,
      errors: [],
    },
  });

  const [importState, setImportState] = useState<ImportState>(getInitialState);

  // Fetch active sheets
  const fetchActiveSheets = async () => {
    try {
      const response = await ordersApisSheetsListActiveSheets({
        query: {
          office_id: selectedOffice?.id,
        }
      });

      if (response.data) {
        setActiveSheets(response.data);
      }
    } catch (error) {
      console.error('Error fetching active sheets:', error);
    }
  };

  // Reset state when component mounts (page refresh or navigation)
  useEffect(() => {
    console.log("Component mounted, resetting import state");
    setImportState(getInitialState());
    fetchActiveSheets();
  }, [selectedOffice?.id]);

  // Debug step changes
  useEffect(() => {
    console.log("Current step changed to:", importState.step);
    console.log("Current import state:", importState);
  }, [importState.step]);

  const handleBack = () => {
    navigate({ to: "/orders" });
  };

  const resetImport = () => {
    console.log("Resetting import state");
    setImportState(getInitialState());
  };

  return (
    <>
      <SiteHeader title="استيراد الطلبات بالجملة" />

      <div className="flex flex-col m-4 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div>
              <h1 className="text-2xl font-bold">استيراد الطلبات بالجملة</h1>
              <p className="text-muted-foreground">
                استيراد عدة طلبات من ملف Excel أو CSV
              </p>
            </div>
          </div>
          <div>
            {importState.step !== ImportStep.UPLOAD && (
              <Button variant="outline" onClick={resetImport}>
                بدء جديد
              </Button>
            )}
            <Button variant="outline" className="mr-2" onClick={handleBack}>
              العودة للطلبات
            </Button>
          </div>
        </div>

        <Separator />

        {/* Progress Steps */}
        <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse">
          {Object.values(ImportStep).map((step, index) => {
            const stepNames = {
              [ImportStep.UPLOAD]: "رفع الملف",
              [ImportStep.PREVIEW]: "معاينة البيانات",
              [ImportStep.MAPPING]: "ربط الأعمدة",
              [ImportStep.PROCESSING]: "معالجة البيانات",
              [ImportStep.RESULTS]: "النتائج",
            };

            const isActive = importState.step === step;
            const isCompleted =
              Object.values(ImportStep).indexOf(importState.step) > index;

            return (
              <div key={step} className="flex items-center">
                <div
                  className={`
                                flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                                ${
                                  isActive
                                    ? "bg-primary text-primary-foreground"
                                    : isCompleted
                                      ? "bg-green-500 text-white"
                                      : "bg-muted text-muted-foreground"
                                }
                            `}
                >
                  {index + 1}
                </div>
                <span
                  className={`ml-2 rtl:mr-2 text-sm ${isActive ? "font-medium" : ""}`}
                >
                  {stepNames[step]}
                </span>
                {index < Object.values(ImportStep).length - 1 && (
                  <div className="w-8 h-px bg-muted mx-4" />
                )}
              </div>
            );
          })}
        </div>

        {/* Sheet Selection */}
        <div className="px-4 lg:px-6">
          <div className="space-y-2">
            <Label htmlFor="sheet_selection">اختيار الورقة</Label>
            <Select
              value={selectedSheetId || 'none'}
              onValueChange={(value) => setSelectedSheetId(value === 'none' ? null : value)}
            >
              <SelectTrigger className="w-full max-w-md">
                <SelectValue placeholder="اختر الورقة (اختياري)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">بدون ورقة</SelectItem>
                {activeSheets.map((sheet) => (
                  <SelectItem key={sheet.id} value={sheet.id}>
                    {sheet.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              اختر الورقة التي تريد إضافة الطلبات إليها. يمكنك ترك هذا الحقل فارغاً إذا كنت لا تريد ربط الطلبات بورقة معينة.
            </p>
          </div>
        </div>

        <Separator />

        {/* Main Content */}
        <div className="flex-1">
          {importState.step === ImportStep.UPLOAD && (
            <FileUploadStep
              importState={importState}
              setImportState={setImportState}
            />
          )}
          {importState.step === ImportStep.PREVIEW && (
            <DataPreviewStep
              importState={importState}
              setImportState={setImportState}
            />
          )}
          {importState.step === ImportStep.MAPPING && (
            <ColumnMappingStep
              importState={importState}
              setImportState={setImportState}
              selectedSheetId={selectedSheetId}
            />
          )}
          {importState.step === ImportStep.PROCESSING && (
            <ProcessingStep
              importState={importState}
              setImportState={setImportState}
              mapRowToOrder={mapRowToOrder}
              selectedSheetId={selectedSheetId}
            />
          )}
          {importState.step === ImportStep.RESULTS && (
            <ResultsStep
              importState={importState}
              setImportState={setImportState}
            />
          )}
        </div>
      </div>
    </>
  );
}

// File upload component
function FileUploadStep({
  importState,
  setImportState,
}: {
  importState: ImportState;
  setImportState: React.Dispatch<React.SetStateAction<ImportState>>;
}) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);

  const handleFileSelect = (file: File) => {
    if (!file) return;

    // Validate file type
    const validTypes = [
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
      "application/vnd.ms-excel", // .xls
      "text/csv", // .csv
      "application/csv",
    ];

    if (
      !validTypes.includes(file.type) &&
      !file.name.match(/\.(xlsx|xls|csv)$/i)
    ) {
      toast.error(
        "نوع الملف غير مدعوم. يرجى اختيار ملف Excel (.xlsx, .xls) أو CSV"
      );
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت");
      return;
    }

    setUploading(true);
    processFile(file);
  };

  const processFile = async (file: File) => {
    try {
      let data: any[][] = [];
      let headers: string[] = [];

      if (file.name.match(/\.(xlsx|xls)$/i)) {
        // Process Excel file
        const arrayBuffer = await file.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        data = jsonData as any[][];
        headers = data[0]?.map((h) => String(h || "")) || [];

        // Remove empty rows
        data = data.filter((row) =>
          row.some((cell) => cell !== null && cell !== undefined && cell !== "")
        );
      } else if (file.name.match(/\.csv$/i)) {
        // Process CSV file
        const text = await file.text();
        const result = Papa.parse<string[]>(text, {
          header: false,
          skipEmptyLines: true,
        });

        data = result.data as any[][];
        headers = data[0]?.map((h) => String(h || "")) || [];
      }

      if (data.length === 0) {
        toast.error("الملف فارغ أو لا يحتوي على بيانات صالحة");
        return;
      }

      if (data.length === 1) {
        toast.error(
          "الملف يحتوي على رؤوس الأعمدة فقط. يرجى إضافة بيانات الطلبات"
        );
        return;
      }

      // Update state with file data
      setImportState((prev) => ({
        ...prev,
        file,
        data,
        headers,
        dataRange: {
          startRow: 0,
          startCol: 0,
          endRow: data.length - 1,
          endCol: headers.length - 1,
        },
        step: ImportStep.PREVIEW,
      }));

      toast.success(
        `تم تحميل الملف بنجاح. تم العثور على ${data.length - 1} صف من البيانات`
      );
    } catch (error) {
      console.error("Error processing file:", error);
      toast.error("حدث خطأ أثناء معالجة الملف. يرجى التأكد من صحة تنسيق الملف");
    } finally {
      setUploading(false);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const removeFile = () => {
    setImportState((prev) => ({
      ...prev,
      file: null,
      data: [],
      headers: [],
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
          <IconFileSpreadsheet className="h-5 w-5" />
          <span>رفع ملف البيانات</span>
        </CardTitle>
        <CardDescription>
          اختر ملف Excel (.xlsx, .xls) أو CSV يحتوي على بيانات الطلبات
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!importState.file ? (
          <div
            className={`
                            border-2 border-dashed rounded-lg p-8 text-center transition-colors
                            ${dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25"}
                            ${uploading ? "opacity-50 pointer-events-none" : "hover:border-primary hover:bg-primary/5"}
                        `}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <IconUpload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">
              {uploading
                ? "جاري معالجة الملف..."
                : "اسحب الملف هنا أو انقر للاختيار"}
            </h3>
            <p className="text-muted-foreground mb-4">
              الأنواع المدعومة: Excel (.xlsx, .xls) أو CSV
            </p>
            <p className="text-sm text-muted-foreground mb-6">
              الحد الأقصى لحجم الملف: 10 ميجابايت
            </p>
            <Button
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
            >
              اختيار ملف
            </Button>
            <Input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleInputChange}
              className="hidden"
            />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <IconFile className="h-8 w-8 text-primary" />
                <div>
                  <p className="font-medium">{importState.file.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {(importState.file.size / 1024).toFixed(1)} كيلوبايت
                  </p>
                </div>
              </div>
              <Button variant="outline" size="sm" onClick={removeFile}>
                <IconX className="h-4 w-4" />
                إزالة
              </Button>
            </div>

            <div className="flex justify-between items-center">
              <p className="text-sm text-muted-foreground">
                تم العثور على {importState.data.length - 1} صف من البيانات
              </p>
              <Button
                onClick={() =>
                  setImportState((prev) => ({
                    ...prev,
                    step: ImportStep.PREVIEW,
                  }))
                }
              >
                التالي: معاينة البيانات
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function DataPreviewStep({
  importState,
  setImportState,
}: {
  importState: ImportState;
  setImportState: React.Dispatch<React.SetStateAction<ImportState>>;
}) {
  const [showAllRows, setShowAllRows] = useState(false);

  // Convert data to spreadsheet format - removed unused preview mapping
  const hasMoreRows = importState.data.length > 20;

  // Get the actual data based on selected range
  const getSelectedData = () => {
    const { startRow, startCol, endRow, endCol } = importState.dataRange;
    return importState.data
      .slice(startRow, endRow + 1)
      .map((row) => row.slice(startCol, endCol + 1));
  };

  const getSelectedHeaders = () => {
    const { startRow, startCol, endCol } = importState.dataRange;
    if (startRow >= importState.data.length) return [];
    return importState.data[startRow]?.slice(startCol, endCol + 1) || [];
  };

  const handleRangeChange = (
    field: keyof ImportState["dataRange"],
    value: number
  ) => {
    setImportState((prev) => ({
      ...prev,
      dataRange: {
        ...prev.dataRange,
        [field]: Math.max(0, value),
      },
    }));
  };

  const handleBack = () => {
    setImportState((prev) => ({ ...prev, step: ImportStep.UPLOAD }));
  };

  const handleNext = () => {
    // Extract the selected data range
    const selectedData = getSelectedData();
    const selectedHeaders = getSelectedHeaders();

    if (selectedData.length === 0 || selectedHeaders.length === 0) {
      toast.error("يرجى تحديد نطاق بيانات صالح");
      return;
    }

    // Update the state with the selected data
    setImportState((prev) => ({
      ...prev,
      data: selectedData,
      headers: selectedHeaders.map((h) => String(h || "")),
      step: ImportStep.MAPPING,
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
          <IconEye className="h-5 w-5" />
          <span>معاينة البيانات</span>
        </CardTitle>
        <CardDescription>
          تأكد من أن البيانات تم تحميلها بشكل صحيح قبل المتابعة
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* File Info */}
        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <IconFile className="h-6 w-6 text-primary" />
            <div>
              <p className="font-medium">{importState.file?.name}</p>
              <p className="text-sm text-muted-foreground">
                {importState.data.length - 1} صف من البيانات،{" "}
                {importState.headers.length} عمود
              </p>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            {(importState.file?.size || 0 / 1024).toFixed(1)} كيلوبايت
          </div>
        </div>

        {/* Data Range Selection */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">تحديد نطاق البيانات</h3>
          <div className="p-4 border rounded-lg bg-blue-50">
            <p className="text-sm text-blue-800 mb-4">
              حدد الصف والعمود الذي تبدأ منه البيانات الفعلية (عادة صف العناوين)
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startRow">صف البداية</Label>
                <Input
                  id="startRow"
                  type="number"
                  min="0"
                  max={importState.data.length - 1}
                  value={importState.dataRange.startRow}
                  onChange={(e) =>
                    handleRangeChange("startRow", parseInt(e.target.value) || 0)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="startCol">عمود البداية</Label>
                <Input
                  id="startCol"
                  type="number"
                  min="0"
                  max={importState.data[0]?.length - 1 || 0}
                  value={importState.dataRange.startCol}
                  onChange={(e) =>
                    handleRangeChange("startCol", parseInt(e.target.value) || 0)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endRow">صف النهاية</Label>
                <Input
                  id="endRow"
                  type="number"
                  min={importState.dataRange.startRow}
                  max={importState.data.length - 1}
                  value={importState.dataRange.endRow}
                  onChange={(e) =>
                    handleRangeChange("endRow", parseInt(e.target.value) || 0)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endCol">عمود النهاية</Label>
                <Input
                  id="endCol"
                  type="number"
                  min={importState.dataRange.startCol}
                  max={importState.data[0]?.length - 1 || 0}
                  value={importState.dataRange.endCol}
                  onChange={(e) =>
                    handleRangeChange("endCol", parseInt(e.target.value) || 0)
                  }
                />
              </div>
            </div>
            <div className="mt-4 space-y-3">
              <div className="text-sm text-blue-700">
                <p>
                  النطاق المحدد: الصفوف {importState.dataRange.startRow + 1} إلى{" "}
                  {importState.dataRange.endRow + 1}، الأعمدة{" "}
                  {importState.dataRange.startCol + 1} إلى{" "}
                  {importState.dataRange.endCol + 1}
                </p>
                <p>
                  عدد صفوف البيانات:{" "}
                  {Math.max(
                    0,
                    importState.dataRange.endRow -
                      importState.dataRange.startRow
                  )}
                </p>
              </div>
              <div className="flex gap-2 flex-wrap">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setImportState((prev) => ({
                      ...prev,
                      dataRange: {
                        startRow: 0,
                        startCol: 0,
                        endRow: prev.data.length - 1,
                        endCol: (prev.data[0]?.length || 1) - 1,
                      },
                    }))
                  }
                >
                  تحديد الكل
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setImportState((prev) => ({
                      ...prev,
                      dataRange: {
                        startRow: 4,
                        startCol: 0,
                        endRow: prev.data.length - 1,
                        endCol: (prev.data[0]?.length || 1) - 1,
                      },
                    }))
                  }
                >
                  بداية من الصف 5
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setImportState((prev) => ({
                      ...prev,
                      dataRange: {
                        startRow: 1,
                        startCol: 0,
                        endRow: prev.data.length - 1,
                        endCol: (prev.data[0]?.length || 1) - 1,
                      },
                    }))
                  }
                >
                  تجاهل الصف الأول
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Data Preview */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">معاينة البيانات المحددة</h3>
            {hasMoreRows && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAllRows(!showAllRows)}
              >
                {showAllRows
                  ? "إظهار أول 20 صف فقط"
                  : `إظهار جميع الصفوف (${importState.data.length})`}
              </Button>
            )}
          </div>

          {/* Spreadsheet Preview */}
          <div className="border rounded-lg overflow-hidden">
            <div className="bg-muted p-2 border-b text-sm">
              <p>
                معاينة النطاق المحدد - العناوين:{" "}
                {getSelectedHeaders().join(" | ")}
              </p>
            </div>
            <div className="max-h-96 overflow-auto">
              <Spreadsheet
                data={getSelectedData()
                  .slice(0, 10)
                  .map((row) => row.map((cell) => ({ value: cell || "" })))}
                columnLabels={getSelectedHeaders().map((h) => String(h || ""))}
                hideRowIndicators={false}
                hideColumnIndicators={false}
                className="w-full"
              />
            </div>
            <div className="bg-muted p-2 border-t text-xs text-muted-foreground">
              يتم عرض أول 10 صفوف من النطاق المحدد فقط
            </div>
          </div>

          {!showAllRows && hasMoreRows && (
            <p className="text-sm text-muted-foreground text-center">
              يتم عرض أول 20 صف فقط. انقر "إظهار جميع الصفوف" لرؤية البيانات
              كاملة.
            </p>
          )}
        </div>

        {/* Data Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border rounded-lg text-center">
            <div className="text-2xl font-bold text-primary">
              {Math.max(
                0,
                importState.dataRange.endRow - importState.dataRange.startRow
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              صف من البيانات (بدون العناوين)
            </div>
          </div>
          <div className="p-4 border rounded-lg text-center">
            <div className="text-2xl font-bold text-primary">
              {getSelectedHeaders().length}
            </div>
            <div className="text-sm text-muted-foreground">عمود محدد</div>
          </div>
          <div className="p-4 border rounded-lg text-center">
            <div className="text-2xl font-bold text-primary">
              {
                getSelectedData()
                  .slice(1)
                  .filter((row) =>
                    row.some(
                      (cell) =>
                        cell !== null && cell !== undefined && cell !== ""
                    )
                  ).length
              }
            </div>
            <div className="text-sm text-muted-foreground">
              صف يحتوي على بيانات
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button variant="outline" onClick={handleBack}>
            <IconArrowLeft className="h-4 w-4 ml-2 rtl:mr-2" />
            السابق
          </Button>
          <Button onClick={handleNext}>
            التالي: ربط الأعمدة
            <IconArrowRight className="h-4 w-4 mr-2 rtl:ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function ColumnMappingStep({
  importState,
  setImportState,
  selectedSheetId,
}: {
  importState: ImportState;
  setImportState: React.Dispatch<React.SetStateAction<ImportState>>;
  selectedSheetId: string | null;
}) {
  // Define available order fields for mapping (based on backend CreateOrderSchema)
  const orderFields = [
    { key: "SKIP", label: "لا تربط هذا العمود", required: false },

    // Required fields (only customer_phone is truly required now)
    { key: "customer_phone", label: "هاتف العميل", required: true },

    // Optional fields (customer_name has default "بدون اسم")
    { key: "customer_name", label: "اسم العميل", required: false },

    // Optional fields (all have defaults in backend schema)
    { key: "customer_address", label: "عنوان العميل", required: false },
    { key: "total_price", label: "السعر الإجمالي", required: false },
    { key: "notes", label: "ملاحظات", required: false },
    {
      key: "commission_fixed_rate",
      label: "معدل العمولة الثابت",
      required: false,
    },
    {
      key: "final_customer_payment",
      label: "الدفع النهائي للعميل",
      required: false,
    },
    { key: "deadline_date", label: "تاريخ الموعد النهائي", required: false },
    { key: "breakable", label: "قابل للكسر", required: false },
  ];

  const handleMappingChange = (header: string, fieldKey: string) => {
    setImportState((prev) => ({
      ...prev,
      mapping: {
        ...prev.mapping,
        [header]: fieldKey,
      },
    }));
  };

  const handleBack = () => {
    setImportState((prev) => ({ ...prev, step: ImportStep.PREVIEW }));
  };

  const handleNext = () => {
    console.log("handleNext called");
    // Validate required fields are mapped
    const requiredFields = orderFields
      .filter((f) => f.required)
      .map((f) => f.key);
    const mappedFields = Object.values(importState.mapping).filter(
      (v) => v !== "" && v !== "SKIP"
    );
    const missingRequired = requiredFields.filter(
      (field) => !mappedFields.includes(field)
    );

    console.log("In handleNext - Required fields:", requiredFields);
    console.log("In handleNext - Mapped fields:", mappedFields);
    console.log("In handleNext - Missing required:", missingRequired);

    if (missingRequired.length > 0) {
      const missingLabels = missingRequired
        .map((key) => orderFields.find((f) => f.key === key)?.label)
        .join("، ");
      console.log("Missing labels:", missingLabels);
      toast.error(`يجب ربط جميع الحقول المطلوبة: ${missingLabels}`);
      return;
    }

    // Additional validation: check if we have data for required fields in at least some rows
    const sampleRows = importState.data.slice(1, 4); // Check first 3 data rows
    console.log("Sample rows:", sampleRows);

    if (sampleRows.length > 0) {
      // Check if at least one row has all required fields filled
      let hasValidRow = false;

      for (const row of sampleRows) {
        const sampleOrder = mapRowToOrder(
          row,
          importState.headers,
          importState.mapping,
          "test",
          selectedSheetId
        );
        console.log("Sample order for row:", sampleOrder);

        // Only validate truly required fields (customer_phone only, since customer_name has default)
        const isValidRow =
          sampleOrder.customer_phone &&
          sampleOrder.customer_phone.trim() !== "";

        if (isValidRow) {
          hasValidRow = true;
          break;
        }
      }

      if (!hasValidRow) {
        console.log("Validation failed - no valid rows found");
        toast.error(
          "لم يتم العثور على صفوف تحتوي على جميع الحقول المطلوبة. تأكد من صحة ربط الأعمدة والبيانات. سيتم تجاهل الصفوف الفارغة أثناء المعالجة."
        );
        // Don't return - allow user to proceed, empty rows will be skipped during processing
      }

      console.log("Found at least one valid row");
    }

    console.log("All validation passed, moving to processing step");
    setImportState((prev) => {
      console.log("Setting step to PROCESSING, current step:", prev.step);
      return { ...prev, step: ImportStep.PROCESSING };
    });
  };

  const getPreviewData = () => {
    // Show first 3 rows of data for preview
    return importState.data.slice(1, 4).map((row) =>
      importState.headers.map((header, index) => ({
        header,
        value: row[index] || "",
        mappedTo: importState.mapping[header] || "SKIP",
      }))
    );
  };

  const requiredFields = orderFields
    .filter((f) => f.required)
    .map((f) => f.key);
  const mappedFields = Object.values(importState.mapping).filter(
    (v) => v !== "" && v !== "SKIP"
  );
  const missingRequired = requiredFields.filter(
    (field) => !mappedFields.includes(field)
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
          <IconLink className="h-5 w-5" />
          <span>ربط الأعمدة</span>
        </CardTitle>
        <CardDescription>
          اربط أعمدة الملف بحقول الطلب المناسبة. الحقل المطلوب فقط: هاتف العميل
          (مميز بعلامة *). اسم العميل اختياري وسيكون "بدون اسم" إذا لم يتم
          تحديده.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Mapping Interface */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">ربط الأعمدة</h3>
          <div className="grid gap-4">
            {importState.headers.map((header, index) => (
              <div
                key={index}
                className="flex items-center space-x-4 rtl:space-x-reverse p-4 border rounded-lg"
              >
                <div className="flex-1">
                  <Label className="text-sm font-medium">عمود: {header}</Label>
                  <p className="text-xs text-muted-foreground mt-1">
                    مثال: {importState.data[1]?.[index] || "لا توجد بيانات"}
                  </p>
                </div>
                <div className="flex-1">
                  <Select
                    value={importState.mapping[header] || "SKIP"}
                    onValueChange={(value) =>
                      handleMappingChange(header, value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر حقل الطلب" />
                    </SelectTrigger>
                    <SelectContent>
                      {orderFields.map((field) => (
                        <SelectItem key={field.key} value={field.key}>
                          {field.label} {field.required && "*"}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Validation Status */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">حالة التحقق</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">الحقول المطلوبة</span>
                <span
                  className={`text-sm ${missingRequired.length === 0 ? "text-green-600" : "text-red-600"}`}
                >
                  {requiredFields.length - missingRequired.length} /{" "}
                  {requiredFields.length}
                </span>
              </div>
              {missingRequired.length > 0 && (
                <p className="text-xs text-red-600 mt-2">
                  مطلوب:{" "}
                  {missingRequired
                    .map((key) => orderFields.find((f) => f.key === key)?.label)
                    .join("، ")}
                </p>
              )}
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">الأعمدة المربوطة</span>
                <span className="text-sm text-blue-600">
                  {mappedFields.length} / {importState.headers.length}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Preview */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">معاينة الربط</h3>
          <div className="border rounded-lg overflow-hidden">
            <div className="bg-muted p-3 border-b">
              <p className="text-sm font-medium">أول 3 صفوف من البيانات</p>
            </div>
            <div className="p-4 space-y-3">
              {getPreviewData().map((row, rowIndex) => (
                <div key={rowIndex} className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">
                    الصف {rowIndex + 2}
                  </p>
                  <div className="grid gap-2">
                    {row.map((cell, cellIndex) => (
                      <div
                        key={cellIndex}
                        className="flex items-center space-x-2 rtl:space-x-reverse text-sm"
                      >
                        <span className="w-24 text-muted-foreground truncate">
                          {cell.header}:
                        </span>
                        <span className="flex-1 font-medium">{cell.value}</span>
                        {cell.mappedTo && cell.mappedTo !== "SKIP" && (
                          <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                            →{" "}
                            {
                              orderFields.find((f) => f.key === cell.mappedTo)
                                ?.label
                            }
                          </span>
                        )}
                        {cell.mappedTo === "SKIP" && (
                          <span className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded">
                            تجاهل
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                  {rowIndex < getPreviewData().length - 1 && <Separator />}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button variant="outline" onClick={handleBack}>
            <IconArrowLeft className="h-4 w-4 ml-2 rtl:mr-2" />
            السابق
          </Button>
          <Button
            onClick={handleNext}
            disabled={missingRequired.length > 0}
            className={
              missingRequired.length > 0 ? "opacity-50 cursor-not-allowed" : ""
            }
          >
            {missingRequired.length > 0
              ? `يجب ربط ${missingRequired.length} حقل مطلوب`
              : "التالي: معالجة البيانات"}
            <IconArrowRight className="h-4 w-4 mr-2 rtl:ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function ProcessingStep({
  importState,
  setImportState,
  mapRowToOrder,
  selectedSheetId,
}: {
  importState: ImportState;
  setImportState: React.Dispatch<React.SetStateAction<ImportState>>;
  mapRowToOrder: (
    row: any[],
    headers: string[],
    mapping: Record<string, string>,
    officeId: string,
    sheetId?: string | null
  ) => CreateOrderSchema;
  selectedSheetId: string | null;
}) {
  console.log("ProcessingStep component rendered");
  const { selectedOffice } = useAuth();
  const [processing, setProcessing] = useState(false);
  const [currentBatch, setCurrentBatch] = useState(0);
  const [totalBatches, setTotalBatches] = useState(0);

  const processOrders = async () => {
    console.log("processOrders called (bulk mode)");
    console.log("selectedOffice:", selectedOffice);
    console.log("importState.data:", importState.data);

    if (!selectedOffice) {
      toast.error("لم يتم اختيار مكتب. يرجى تسجيل الدخول مرة أخرى");
      return;
    }

    setProcessing(true);

    // Clear previous results first
    setImportState((prev) => ({
      ...prev,
      results: {
        total: 0,
        successful: 0,
        failed: 0,
        errors: [],
      },
    }));

    try {
      // Skip header row and prepare all orders for bulk creation
      const dataRows = importState.data.slice(1);
      console.log("dataRows to process:", dataRows.length);

      // Step 1: Validate and prepare all orders
      const validOrders: CreateOrderSchema[] = [];
      const invalidRows: Array<{ row: any; index: number; error: string }> = [];

      console.log("Step 1: Validating and preparing orders...");
      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i];

        try {
          // Map row data to order fields
          const orderData = mapRowToOrder(
            row,
            importState.headers,
            importState.mapping,
            selectedOffice.id,
            selectedSheetId
          );

          // Validate required fields (only customer_phone is truly required now)
          // customer_name always has a default value "بدون اسم" so it's never empty
          const missingFields = [];
          if (
            !orderData.customer_phone ||
            orderData.customer_phone.trim() === ""
          ) {
            missingFields.push("هاتف العميل");
          }
          // Note: customer_name now has default "بدون اسم", code and customer_address are optional

          if (missingFields.length > 0) {
            invalidRows.push({
              row,
              index: i + 2, // +2 because we skip header and arrays are 0-indexed
              error: `حقول مطلوبة مفقودة: ${missingFields.join("، ")}`,
            });
          } else {
            validOrders.push(orderData);
          }
        } catch (error: any) {
          invalidRows.push({
            row,
            index: i + 2,
            error: `خطأ في معالجة البيانات: ${error.message || "خطأ غير معروف"}`,
          });
        }
      }

      console.log(
        `Validation complete - Valid orders: ${validOrders.length}, Invalid rows: ${invalidRows.length}`
      );

      // Step 2: Process orders in batches (backend limit is 100 orders per batch)
      const batchSize = 100;
      const batches = [];
      for (let i = 0; i < validOrders.length; i += batchSize) {
        batches.push(validOrders.slice(i, i + batchSize));
      }

      console.log(`Step 2: Processing ${batches.length} batches...`);
      setTotalBatches(batches.length);

      const allCreatedOrders: any[] = [];
      const allFailedOrders: Array<{ row: any; index: number; error: string }> =
        [...invalidRows];

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        setCurrentBatch(batchIndex + 1);

        console.log(
          `Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} orders`
        );

        try {
          // Call bulk API
          const response = await ordersApisOrdersCreateOrdersBulk({
            body: batch,
          });

          console.log("Bulk API response:", response);

          if (response.data) {
            const bulkResult = response.data;

            // Add successful orders
            allCreatedOrders.push(...bulkResult.created_orders);

            // Add failed orders from this batch
            if (
              bulkResult.failed_orders &&
              bulkResult.failed_orders.length > 0
            ) {
              bulkResult.failed_orders.forEach((failedOrder: any) => {
                allFailedOrders.push({
                  row: batch[failedOrder.index] || {},
                  index: failedOrder.index + batchIndex * batchSize + 2,
                  error:
                    failedOrder.error || failedOrder.details || "خطأ غير معروف",
                });
              });
            }

            console.log(
              `Batch ${batchIndex + 1} completed - Created: ${bulkResult.successful_count}, Failed: ${bulkResult.failed_count}`
            );
          } else {
            console.error("Invalid response from bulk API");
            // Mark all orders in this batch as failed
            batch.forEach((order, index) => {
              allFailedOrders.push({
                row: order,
                index: batchIndex * batchSize + index + 2,
                error: "فشل في إنشاء الطلب - استجابة غير صالحة من الخادم",
              });
            });
          }
        } catch (error: any) {
          console.error("Error in bulk API call:", error);
          let errorMessage = "خطأ في الخادم";

          if (error.response?.data) {
            if (typeof error.response.data === "string") {
              errorMessage = error.response.data;
            } else if (error.response.data.detail) {
              errorMessage = error.response.data.detail;
            } else if (
              error.response.data.errors &&
              error.response.data.errors.length > 0
            ) {
              errorMessage = error.response.data.errors.join(", ");
            } else {
              errorMessage = JSON.stringify(error.response.data);
            }
          } else if (error.message) {
            errorMessage = error.message;
          }

          // Mark all orders in this batch as failed
          batch.forEach((order, index) => {
            allFailedOrders.push({
              row: order,
              index: batchIndex * batchSize + index + 2,
              error: errorMessage,
            });
          });
        }
      }

      // Step 3: Compile final results
      console.log("Step 3: Compiling final results...");
      const totalProcessed = dataRows.length;
      const successful = allCreatedOrders.length;
      const failed = allFailedOrders.length;

      console.log(
        `Final results - Total: ${totalProcessed}, Successful: ${successful}, Failed: ${failed}`
      );

      // Update state with results
      setImportState((prev) => ({
        ...prev,
        results: {
          total: totalProcessed,
          successful,
          failed,
          errors: allFailedOrders.map((item) => ({
            row: item.index,
            data: item.row,
            error: item.error,
          })),
        },
        step: ImportStep.RESULTS,
      }));

      // Show success/error messages
      if (successful > 0) {
        toast.success(`تم إنشاء ${successful} طلب بنجاح`);
      }
      if (failed > 0) {
        toast.error(`فشل في إنشاء ${failed} طلب`);
      }
    } catch (error: any) {
      console.error("Unexpected error in processOrders:", error);
      toast.error("حدث خطأ غير متوقع أثناء معالجة الطلبات");

      // Set all as failed
      setImportState((prev) => ({
        ...prev,
        results: {
          total: importState.data.length - 1,
          successful: 0,
          failed: importState.data.length - 1,
          errors: [
            {
              row: 1,
              data: {},
              error: error.message || "خطأ غير معروف",
            },
          ],
        },
        step: ImportStep.RESULTS,
      }));
    } finally {
      setProcessing(false);
    }
  };

  const progress =
    processing && totalBatches > 0 ? (currentBatch / totalBatches) * 100 : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
          <IconLoader2
            className={`h-5 w-5 ${processing ? "animate-spin" : ""}`}
          />
          <span>معالجة الطلبات (المعالجة المجمعة)</span>
        </CardTitle>
        <CardDescription>
          {processing
            ? "جاري إنشاء الطلبات باستخدام المعالجة المجمعة..."
            : "جاهز لبدء المعالجة المجمعة"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {!processing && (
          <div className="text-center py-8">
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h3 className="font-medium text-blue-900 mb-2">
                  جاهز للمعالجة المجمعة
                </h3>
                <p className="text-sm text-blue-700">
                  سيتم إنشاء {importState.data.length - 1} طلب جديد في المكتب:{" "}
                  {selectedOffice?.name}
                </p>
                <p className="text-xs text-blue-600 mt-2">
                  المعالجة المجمعة أسرع وأكثر كفاءة من المعالجة الفردية
                </p>
              </div>
              <Button onClick={processOrders} size="lg">
                بدء معالجة الطلبات
              </Button>
            </div>
          </div>
        )}

        {processing && (
          <div className="space-y-4">
            <div className="text-center">
              <p className="text-lg font-medium mb-2">
                معالجة المجموعة {currentBatch} من {totalBatches}
              </p>
              <Progress value={progress} className="w-full" />
              <p className="text-sm text-muted-foreground mt-2">
                {Math.round(progress)}% مكتمل
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                كل مجموعة تحتوي على حتى 100 طلب
              </p>
            </div>

            <div className="p-4 bg-yellow-50 rounded-lg">
              <p className="text-sm text-yellow-800">
                جاري المعالجة المجمعة... يرجى عدم إغلاق الصفحة
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function ResultsStep({
  importState,
  setImportState,
}: {
  importState: ImportState;
  setImportState: React.Dispatch<React.SetStateAction<ImportState>>;
}) {
  const navigate = useNavigate();

  const handleStartNew = () => {
    console.log("Starting new import");
    setImportState({
      step: ImportStep.UPLOAD,
      file: null,
      data: [],
      headers: [],
      mapping: {},
      dataRange: {
        startRow: 0,
        startCol: 0,
        endRow: 0,
        endCol: 0,
      },
      results: {
        total: 0,
        successful: 0,
        failed: 0,
        errors: [],
      },
    });
  };

  const handleGoToOrders = () => {
    navigate({ to: "/orders" });
  };

  const exportErrors = () => {
    if (importState.results.errors.length === 0) return;

    const errorData = [
      ["رقم الصف", "البيانات", "سبب الخطأ"],
      ...importState.results.errors.map((error) => [
        error.row.toString(),
        Array.isArray(error.data)
          ? error.data.join(" | ")
          : JSON.stringify(error.data),
        error.error,
      ]),
    ];

    const ws = XLSX.utils.aoa_to_sheet(errorData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "أخطاء الاستيراد");
    XLSX.writeFile(
      wb,
      `أخطاء_استيراد_الطلبات_${new Date().toISOString().split("T")[0]}.xlsx`
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
          <IconCheck className="h-5 w-5 text-green-600" />
          <span>نتائج الاستيراد</span>
        </CardTitle>
        <CardDescription>ملخص عملية استيراد الطلبات</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-6 border rounded-lg text-center bg-blue-50">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {importState.results.total}
            </div>
            <div className="text-sm text-blue-700 font-medium">
              إجمالي الصفوف
            </div>
          </div>
          <div className="p-6 border rounded-lg text-center bg-green-50">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {importState.results.successful}
            </div>
            <div className="text-sm text-green-700 font-medium">
              تم إنشاؤها بنجاح
            </div>
          </div>
          <div className="p-6 border rounded-lg text-center bg-red-50">
            <div className="text-3xl font-bold text-red-600 mb-2">
              {importState.results.failed}
            </div>
            <div className="text-sm text-red-700 font-medium">
              فشل في الإنشاء
            </div>
          </div>
        </div>

        {/* Success Message */}
        {importState.results.successful > 0 && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <IconCheck className="h-5 w-5 text-green-600" />
              <h3 className="font-medium text-green-800">
                تم إنشاء {importState.results.successful} طلب بنجاح!
              </h3>
            </div>
            <p className="text-sm text-green-700 mt-2">
              يمكنك الآن العثور على الطلبات الجديدة في قائمة الطلبات.
            </p>
          </div>
        )}

        {/* Error Details */}
        {importState.results.failed > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-red-800">
                الطلبات التي فشل إنشاؤها ({importState.results.failed})
              </h3>
              <Button variant="outline" size="sm" onClick={exportErrors}>
                تصدير الأخطاء إلى Excel
              </Button>
            </div>

            <div className="border rounded-lg overflow-hidden">
              <div className="bg-red-50 p-3 border-b">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <IconAlertTriangle className="h-5 w-5 text-red-600" />
                  <span className="font-medium text-red-800">
                    تفاصيل الأخطاء
                  </span>
                </div>
              </div>
              <div className="max-h-96 overflow-auto">
                {importState.results.errors.map((error, index) => (
                  <div key={index} className="p-4 border-b last:border-b-0">
                    <div className="flex items-start space-x-3 rtl:space-x-reverse">
                      <div className="flex-shrink-0 w-12 h-8 bg-red-100 rounded flex items-center justify-center">
                        <span className="text-sm font-medium text-red-600">
                          {error.row}
                        </span>
                      </div>
                      <div className="flex-1 space-y-2">
                        <div className="text-sm">
                          <span className="font-medium text-red-800">
                            خطأ:{" "}
                          </span>
                          <span className="text-red-700">{error.error}</span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          <span className="font-medium">البيانات: </span>
                          {Array.isArray(error.data)
                            ? error.data.slice(0, 3).join(" | ") +
                              (error.data.length > 3 ? "..." : "")
                            : JSON.stringify(error.data).substring(0, 100) +
                              "..."}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* File Info */}
        <div className="p-4 bg-muted/50 rounded-lg">
          <h3 className="font-medium mb-2">معلومات الملف المعالج</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">اسم الملف: </span>
              <span className="font-medium">{importState.file?.name}</span>
            </div>
            <div>
              <span className="text-muted-foreground">حجم الملف: </span>
              <span className="font-medium">
                {((importState.file?.size || 0) / 1024).toFixed(1)} كيلوبايت
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">عدد الأعمدة: </span>
              <span className="font-medium">{importState.headers.length}</span>
            </div>
            <div>
              <span className="text-muted-foreground">وقت المعالجة: </span>
              <span className="font-medium">
                {new Date().toLocaleString("ar-SA")}
              </span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button onClick={handleGoToOrders} size="lg">
            عرض جميع الطلبات
          </Button>
          <Button variant="outline" onClick={handleStartNew} size="lg">
            استيراد ملف جديد
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
