import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { IconLoader2, IconMapPin, IconUser, IconPackage, IconCheck } from '@tabler/icons-react'
import { ordersApisOrdersListOrders, ordersApisOrdersUpdateOrder, accountsApisListUsers } from '@/client'
import { type OrderSchema, type UserSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { groupAddresses, type AddressGroup } from '@/lib/fuzzy-match'

export const Route = createFileRoute('/orders/assign')({
    component: RouteComponent,
})

interface GroupedOrder {
    group: AddressGroup;
    orders: OrderSchema[];
}

interface CommissionDialogData {
    isOpen: boolean;
    selectedOrders: OrderSchema[];
    selectedEmployee: UserSchema | null;
    customCommissionRate: string;
}

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const navigate = useNavigate()
    const [loading, setLoading] = useState(false)
    const [assigning, setAssigning] = useState(false)
    const [orders, setOrders] = useState<OrderSchema[]>([])
    const [employees, setEmployees] = useState<UserSchema[]>([])
    const [groupedOrders, setGroupedOrders] = useState<GroupedOrder[]>([])
    const [selectedEmployee, setSelectedEmployee] = useState<string>('')
    const [selectedOrderIds, setSelectedOrderIds] = useState<Set<string>>(new Set())
    const [selectedGroupIds, setSelectedGroupIds] = useState<Set<string>>(new Set())
    const [viewMode, setViewMode] = useState<'individual' | 'grouped'>('grouped')

    const [commissionDialog, setCommissionDialog] = useState<CommissionDialogData>({
        isOpen: false,
        selectedOrders: [],
        selectedEmployee: null,
        customCommissionRate: ''
    })

    // Fetch unassigned orders
    const fetchUnassignedOrders = async () => {
        if (!selectedOffice) return

        setLoading(true)
        try {
            const response = await ordersApisOrdersListOrders({
                query: {
                    office_id: selectedOffice.id,
                    assigned_to_isnull: true,
                    page_size: 1000, // Get all unassigned orders
                },
            })

            if (response.data) {
                setOrders(response.data.orders)
                groupOrdersByAddress(response.data.orders)
            }
        } catch (error) {
            console.error('Error fetching unassigned orders:', error)
            toast.error('حدث خطأ أثناء تحميل الطلبات غير المخصصة')
        } finally {
            setLoading(false)
        }
    }

    // Fetch employees
    const fetchEmployees = async () => {
        if (!selectedOffice) return

        try {
            const response = await accountsApisListUsers({
                query: {
                    office_id: selectedOffice.id,
                    page_size: 1000,
                },
            })

            if (response.data) {
                // Filter only employees (not admins/managers)
                const employeeUsers = response.data.users.filter(user => user.role === 'employee')
                setEmployees(employeeUsers)
            }
        } catch (error) {
            console.error('Error fetching employees:', error)
            toast.error('حدث خطأ أثناء تحميل الموظفين')
        }
    }

    // Group orders by address using fuzzy matching
    const groupOrdersByAddress = (orderList: OrderSchema[]) => {
        const addressesWithIds = orderList.map(order => ({
            id: order.id,
            address: order.customer_address || 'عنوان غير محدد'
        }))

        const addressGroups = groupAddresses(addressesWithIds, { threshold: 0.7 })

        const grouped = addressGroups.map(group => ({
            group,
            orders: orderList.filter(order => group.orders.includes(order.id))
        }))

        setGroupedOrders(grouped)
    }

    useEffect(() => {
        fetchUnassignedOrders()
        fetchEmployees()
    }, [selectedOffice])

    // Handle individual order selection
    const handleOrderSelection = (orderId: string, checked: boolean) => {
        const newSelected = new Set(selectedOrderIds)
        if (checked) {
            newSelected.add(orderId)
        } else {
            newSelected.delete(orderId)
        }
        setSelectedOrderIds(newSelected)
    }

    // Handle group selection
    const handleGroupSelection = (group: AddressGroup, checked: boolean) => {
        const newSelected = new Set(selectedGroupIds)
        const newOrderSelected = new Set(selectedOrderIds)

        if (checked) {
            newSelected.add(group.representativeAddress)
            group.orders.forEach(orderId => newOrderSelected.add(orderId))
        } else {
            newSelected.delete(group.representativeAddress)
            group.orders.forEach(orderId => newOrderSelected.delete(orderId))
        }

        setSelectedGroupIds(newSelected)
        setSelectedOrderIds(newOrderSelected)
    }

    // Handle employee selection
    const handleEmployeeChange = (employeeId: string) => {
        setSelectedEmployee(employeeId)
    }

    // Open commission dialog
    const handleAssignOrders = () => {
        if (!selectedEmployee) {
            toast.error('يرجى اختيار موظف')
            return
        }

        if (selectedOrderIds.size === 0) {
            toast.error('يرجى اختيار طلبات للتخصيص')
            return
        }

        const selectedEmployeeData = employees.find(emp => emp.id === selectedEmployee)
        if (!selectedEmployeeData) {
            toast.error('الموظف المحدد غير موجود')
            return
        }

        const selectedOrdersData = orders.filter(order => selectedOrderIds.has(order.id))

        setCommissionDialog({
            isOpen: true,
            selectedOrders: selectedOrdersData,
            selectedEmployee: selectedEmployeeData,
            customCommissionRate: ''
        })
    }

    // Assign orders with commission rate
    const handleConfirmAssignment = async () => {
        if (!commissionDialog.selectedEmployee) return

        setAssigning(true)
        try {
            const customRate = commissionDialog.customCommissionRate
                ? parseFloat(commissionDialog.customCommissionRate)
                : null

            // Assign each order individually
            for (const order of commissionDialog.selectedOrders) {
                await ordersApisOrdersUpdateOrder({
                    path: { order_id: order.id },
                    body: {
                        orders_sheet_id: order.orders_sheet_id,
                        notes: order.notes,
                        total_price: order.total_price,
                        customer_name: order.customer_name,
                        customer_phone: order.customer_phone,
                        customer_address: order.customer_address,
                        customer_company_id: order.customer_company_id,
                        breakable: order.breakable,
                        deadline_date: order.deadline_date,
                        commission_fixed_rate: customRate || order.commission_fixed_rate,
                        assigned_to_id: commissionDialog.selectedEmployee.id,
                        final_customer_payment: order.final_customer_payment,
                        handling_status: order.handling_status,
                        status_template_id: order.status_template_id,
                        status_reason: order.status_reason,
                    },
                })
            }

            toast.success(`تم تخصيص ${commissionDialog.selectedOrders.length} طلب بنجاح`)

            // Reset state
            setSelectedOrderIds(new Set())
            setSelectedGroupIds(new Set())
            setSelectedEmployee('')
            setCommissionDialog({
                isOpen: false,
                selectedOrders: [],
                selectedEmployee: null,
                customCommissionRate: ''
            })

            // Refresh orders
            fetchUnassignedOrders()
        } catch (error) {
            console.error('Error assigning orders:', error)
            toast.error('حدث خطأ أثناء تخصيص الطلبات')
        } finally {
            setAssigning(false)
        }
    }

    const handleCancelAssignment = () => {
        setCommissionDialog({
            isOpen: false,
            selectedOrders: [],
            selectedEmployee: null,
            customCommissionRate: ''
        })
    }

    const getSelectedOrdersCount = () => selectedOrderIds.size
    const getSelectedGroupsCount = () => selectedGroupIds.size

    return (
        <div className="container mx-auto p-6 space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">تخصيص سريع للطلبات</h1>
                    <p className="text-gray-600 mt-1">تخصيص الطلبات غير المخصصة للموظفين</p>
                </div>
                <Button
                    variant="outline"
                    onClick={() => navigate({ to: '/orders' })}
                >
                    العودة للطلبات
                </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            <IconPackage className="h-5 w-5 text-blue-500" />
                            <div>
                                <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                                <p className="text-2xl font-bold text-gray-900">{orders.length}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            <IconMapPin className="h-5 w-5 text-green-500" />
                            <div>
                                <p className="text-sm font-medium text-gray-600">مجموعات العناوين</p>
                                <p className="text-2xl font-bold text-gray-900">{groupedOrders.length}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            <IconUser className="h-5 w-5 text-purple-500" />
                            <div>
                                <p className="text-sm font-medium text-gray-600">الموظفين المتاحين</p>
                                <p className="text-2xl font-bold text-gray-900">{employees.length}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            <IconCheck className="h-5 w-5 text-orange-500" />
                            <div>
                                <p className="text-sm font-medium text-gray-600">الطلبات المحددة</p>
                                <p className="text-2xl font-bold text-gray-900">{getSelectedOrdersCount()}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Controls */}
            <Card>
                <CardHeader>
                    <CardTitle>إعدادات التخصيص</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* View Mode Toggle */}
                        <div className="space-y-2">
                            <Label>وضع العرض</Label>
                            <div className="flex space-x-2">
                                <Button
                                    variant={viewMode === 'grouped' ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => setViewMode('grouped')}
                                >
                                    مجمعة بالعناوين ({getSelectedGroupsCount()})
                                </Button>
                                <Button
                                    variant={viewMode === 'individual' ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => setViewMode('individual')}
                                >
                                    فردية ({getSelectedOrdersCount()})
                                </Button>
                            </div>
                        </div>

                        {/* Employee Selection */}
                        <div className="space-y-2">
                            <Label>اختيار الموظف</Label>
                            <Select value={selectedEmployee} onValueChange={handleEmployeeChange}>
                                <SelectTrigger>
                                    <SelectValue placeholder="اختر موظف..." />
                                </SelectTrigger>
                                <SelectContent>
                                    {employees.map((employee) => (
                                        <SelectItem key={employee.id} value={employee.id}>
                                            <div className="flex items-center justify-between w-full">
                                                <span>{employee.first_name} {employee.last_name}</span>
                                                <Badge variant="secondary" className="ml-2">
                                                    {employee.commission_fixed_rate} ج.م
                                                </Badge>
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Assign Button */}
                        <div className="flex items-end">
                            <Button
                                onClick={handleAssignOrders}
                                disabled={!selectedEmployee || selectedOrderIds.size === 0}
                                className="w-full"
                            >
                                {assigning ? (
                                    <>
                                        <IconLoader2 className="mr-2 h-4 w-4 animate-spin" />
                                        جاري التخصيص...
                                    </>
                                ) : (
                                    `تخصيص ${getSelectedOrdersCount()} طلب`
                                )}
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Orders List */}
            <Card>
                <CardHeader>
                    <CardTitle>
                        {viewMode === 'grouped' ? 'الطلبات مجمعة بالعناوين' : 'الطلبات الفردية'}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="flex items-center justify-center py-8">
                            <IconLoader2 className="h-8 w-8 animate-spin text-blue-500" />
                            <span className="ml-2">جاري التحميل...</span>
                        </div>
                    ) : viewMode === 'grouped' ? (
                        <div className="space-y-4">
                            {groupedOrders.map((groupedOrder) => (
                                <div key={groupedOrder.group.representativeAddress} className="border rounded-lg p-4">
                                    <div className="flex items-center space-x-3 mb-3">
                                        <Checkbox
                                            checked={selectedGroupIds.has(groupedOrder.group.representativeAddress)}
                                            onCheckedChange={(checked) =>
                                                handleGroupSelection(groupedOrder.group, checked as boolean)
                                            }
                                        />
                                        <div className="flex-1">
                                            <h3 className="font-medium text-gray-900">
                                                {groupedOrder.group.representativeAddress}
                                            </h3>
                                            <p className="text-sm text-gray-500">
                                                {groupedOrder.orders.length} طلب في هذا العنوان
                                            </p>
                                        </div>
                                        <Badge variant="secondary">
                                            {groupedOrder.orders.length} طلب
                                        </Badge>
                                    </div>

                                    <div className="ml-6 space-y-2">
                                        {groupedOrder.orders.map((order) => (
                                            <div key={order.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                                <div className="flex items-center space-x-2">
                                                    <Checkbox
                                                        checked={selectedOrderIds.has(order.id)}
                                                        onCheckedChange={(checked) =>
                                                            handleOrderSelection(order.id, checked as boolean)
                                                        }
                                                    />
                                                    <div>
                                                        <p className="font-medium">{order.customer_name}</p>
                                                        <p className="text-sm text-gray-500">{order.id.slice(-8)}</p>
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <p className="font-medium">{order.total_price} ج.م</p>
                                                    <p className="text-sm text-gray-500">{order.customer_phone}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="space-y-2">
                            {orders.map((order) => (
                                <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                                    <div className="flex items-center space-x-3">
                                        <Checkbox
                                            checked={selectedOrderIds.has(order.id)}
                                            onCheckedChange={(checked) =>
                                                handleOrderSelection(order.id, checked as boolean)
                                            }
                                        />
                                        <div>
                                            <p className="font-medium">{order.customer_name}</p>
                                            <p className="text-sm text-gray-500">{order.id.slice(-8)}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-4">
                                        <div className="text-right">
                                            <p className="font-medium">{order.total_price} ج.م</p>
                                            <p className="text-sm text-gray-500">{order.customer_phone}</p>
                                        </div>
                                        <div className="text-sm text-gray-500 max-w-xs truncate">
                                            {order.customer_address}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Commission Rate Dialog */}
            <Dialog open={commissionDialog.isOpen} onOpenChange={handleCancelAssignment}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>تخصيص نسبة العمولة</DialogTitle>
                    </DialogHeader>

                    <div className="space-y-4">
                        <div>
                            <Label>الموظف المحدد</Label>
                            <p className="text-sm text-gray-600 mt-1">
                                {commissionDialog.selectedEmployee?.first_name} {commissionDialog.selectedEmployee?.last_name}
                            </p>
                            <p className="text-sm text-gray-500">
                                نسبة العمولة الافتراضية: {commissionDialog.selectedEmployee?.commission_fixed_rate} ج.م
                            </p>
                        </div>

                        <Separator />

                        <div>
                            <Label>الطلبات المحددة ({commissionDialog.selectedOrders.length})</Label>
                            <div className="mt-2 max-h-32 overflow-y-auto space-y-1">
                                {commissionDialog.selectedOrders.map((order) => (
                                    <div key={order.id} className="text-sm text-gray-600">
                                        • {order.customer_name} - {order.id.slice(-8)}
                                    </div>
                                ))}
                            </div>
                        </div>

                        <Separator />

                        <div>
                            <Label htmlFor="commission-rate">نسبة العمولة المخصصة (اختياري)</Label>
                            <Input
                                id="commission-rate"
                                type="number"
                                placeholder="اترك فارغاً لاستخدام النسبة الافتراضية"
                                value={commissionDialog.customCommissionRate}
                                onChange={(e) => setCommissionDialog(prev => ({
                                    ...prev,
                                    customCommissionRate: e.target.value
                                }))}
                            />
                            <p className="text-xs text-gray-500 mt-1">
                                إذا تركت هذا الحقل فارغاً، سيتم استخدام نسبة العمولة الافتراضية للموظف
                            </p>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" onClick={handleCancelAssignment}>
                            إلغاء
                        </Button>
                        <Button onClick={handleConfirmAssignment} disabled={assigning}>
                            {assigning ? (
                                <>
                                    <IconLoader2 className="mr-2 h-4 w-4 animate-spin" />
                                    جاري التخصيص...
                                </>
                            ) : (
                                'تأكيد التخصيص'
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
