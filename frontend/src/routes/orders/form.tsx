import { SiteHeader } from '@/components/site-header'
import { createFileRoute, useNavigate, useSearch } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useEffect, useState } from 'react'
import { ordersApisOrdersGetOrder, ordersApisOrdersCreateOrder, ordersApisOrdersUpdateOrder, ordersApisCancellationTemplatesListCancellationTemplates, ordersApisSheetsListActiveSheets } from '@/client'
import { type OrderSchema, type OrderHandlingStatusSchema, type OrderStatusTemplateSchema, type OrdersSheetSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import { Separator } from '@/components/ui/separator'

export const Route = createFileRoute('/orders/form')({
    component: RouteComponent,
    validateSearch: (search: Record<string, unknown>) => ({
        id: search.id as string | undefined,
    }),
})

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const navigate = useNavigate()
    const { id } = useSearch({ from: '/orders/form' })
    const [loading, setLoading] = useState(false)
    const [cancellationTemplates, setCancellationTemplates] = useState<OrderStatusTemplateSchema[]>([])
    const [activeSheets, setActiveSheets] = useState<OrdersSheetSchema[]>([])

    const [order, setOrder] = useState<Partial<OrderSchema>>({
        notes: '',
        total_price: null,
        customer_name: '',
        customer_phone: '',
        customer_address: '',
        customer_company_id: null,
        orders_sheet_id: null,
        breakable: false,
        deadline_date: null,
        commission_fixed_rate: null,
        assigned_to_id: null,
        final_customer_payment: null,
        handling_status: 'PENDING' as OrderHandlingStatusSchema,
        status_template_id: null,
        status_reason: null,
    })
    const [companyCode, setCompanyCode] = useState('')

    const isEditing = !!id

    useEffect(() => {
        fetchCancellationTemplates()
        fetchActiveSheets()
        if (isEditing && id) {
            fetchOrder()
        }
    }, [id])

    const fetchCancellationTemplates = async () => {
        try {
            const response = await ordersApisCancellationTemplatesListCancellationTemplates({
                query: {
                    office_id: selectedOffice?.id,
                    page_size: 100,
                },
            })
            if (response.data) {
                setCancellationTemplates(response.data.templates)
            }
        } catch (error) {
            console.error('Error fetching cancellation templates:', error)
            toast.error('حدث خطأ أثناء تحميل قوالب الإلغاء')
        }
    }

    const fetchActiveSheets = async () => {
        try {
            const response = await ordersApisSheetsListActiveSheets({
                query: {
                    office_id: selectedOffice?.id,
                }
            })

            if (response.data) {
                setActiveSheets(response.data)
            }
        } catch (error) {
            console.error('Error fetching active sheets:', error)
        }
    }



    const fetchOrder = async () => {
        try {
            setLoading(true)
            const response = await ordersApisOrdersGetOrder({
                path: {
                    order_id: id!,
                },
            })
            if (response.data) {
                setOrder(response.data)
                // Set company code from the company data if available
                const orderData = response.data as any
                if (orderData.customer_company) {
                    setCompanyCode(orderData.customer_company.code || orderData.customer_company.name || '')
                }
            }
        } catch (error) {
            console.error('Error fetching order:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات الطلب')
        } finally {
            setLoading(false)
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!selectedOffice) {
            toast.error('يرجى اختيار مكتب')
            return
        }

        try {
            setLoading(true)

            if (isEditing) {
                await ordersApisOrdersUpdateOrder({
                    path: { order_id: id! },
                    body: {
                        orders_sheet_id: order.orders_sheet_id ?? null,
                        notes: order.notes!,
                        total_price: order.total_price ?? null,
                        customer_name: order.customer_name!,
                        customer_phone: order.customer_phone!,
                        customer_address: order.customer_address ?? '',
                        customer_company_id: order.customer_company_id ?? null,
                        breakable: order.breakable!,
                        deadline_date: order.deadline_date ?? null,
                        commission_fixed_rate: order.commission_fixed_rate ?? null,
                        assigned_to_id: order.assigned_to_id ?? null,
                        final_customer_payment: order.final_customer_payment ?? null,
                        handling_status: order.handling_status!,
                        status_template_id: order.status_template_id ?? null,
                        status_reason: order.status_reason ?? null,
                    },
                })
                toast.success('تم تحديث الطلب بنجاح')
            } else {
                await ordersApisOrdersCreateOrder({
                    body: {
                        office_id: selectedOffice.id,
                        orders_sheet_id: order.orders_sheet_id ?? null,
                        notes: order.notes!,
                        total_price: order.total_price ?? null,
                        customer_name: order.customer_name!,
                        customer_phone: order.customer_phone!,
                        customer_address: order.customer_address ?? '',
                        customer_company_id: null, // Always null when using company_code
                        company_code: companyCode.trim(),
                        breakable: order.breakable!,
                        deadline_date: order.deadline_date ?? null,
                        commission_fixed_rate: order.commission_fixed_rate ?? null,
                        assigned_to_id: order.assigned_to_id ?? null,
                        final_customer_payment: null, // Always null when creating
                        handling_status: order.handling_status!,
                        status_template_id: order.status_template_id ?? null,
                        status_reason: order.status_reason ?? null,
                    },
                })
                toast.success('تم إنشاء الطلب بنجاح')
            }

            navigate({ to: '/orders' })
        } catch (error) {
            console.error('Error saving order:', error)
            toast.error('حدث خطأ أثناء حفظ الطلب')
        } finally {
            setLoading(false)
        }
    }

    const handleInputChange = (field: keyof OrderSchema, value: any) => {
        setOrder(prev => ({ ...prev, [field]: value }))
    }

    const handleCancellationTemplateChange = async (templateId: string) => {
        if (!isEditing || !id) {
            toast.error('لا يمكن تطبيق قالب الإلغاء إلا عند تعديل طلب موجود')
            return
        }

        const template = cancellationTemplates.find(t => t.id === templateId)
        if (!template) return

        try {
            setLoading(true)

            await ordersApisOrdersUpdateOrder({
                path: { order_id: id },
                body: {
                    orders_sheet_id: order.orders_sheet_id ?? null,
                    notes: order.notes!,
                    total_price: order.total_price ?? null,
                    customer_name: order.customer_name!,
                    customer_phone: order.customer_phone!,
                    customer_address: order.customer_address ?? '',
                    customer_company_id: order.customer_company_id ?? null,
                    breakable: order.breakable!,
                    deadline_date: order.deadline_date ?? null,
                    commission_fixed_rate: order.commission_fixed_rate ?? null,
                    assigned_to_id: order.assigned_to_id ?? null,
                    final_customer_payment: order.final_customer_payment ?? null,
                    handling_status: template.order_default_handling_status || 'CANCELLED',
                    status_template_id: template.id,
                    status_reason: template.reason_template_text,
                },
            })

            toast.success('تم تطبيق قالب الإلغاء بنجاح')
            navigate({ to: '/orders' })
        } catch (error) {
            console.error('Error applying cancellation template:', error)
            toast.error('حدث خطأ أثناء تطبيق قالب الإلغاء')
        } finally {
            setLoading(false)
        }
    }



    const formatDateForInput = (dateString: string | null | undefined) => {
        if (!dateString) return ''
        const date = new Date(dateString)
        return date.toISOString().slice(0, 16) // Format for datetime-local input
    }

    const parseDateFromInput = (dateString: string) => {
        if (!dateString) return null
        return new Date(dateString).toISOString()
    }

    return <>
        <SiteHeader title={"إدارة الطلبات"} />
        <div className="flex flex-col m-4">
            <h1 className="text-2xl font-bold">{isEditing ? "تعديل الطلب" : "إضافة طلب جديد"}</h1>
            <p className="text-sm text-muted-foreground">
                {isEditing ? "قم بتعديل بيانات الطلب" : "أدخل بيانات الطلب الجديد"}
            </p>
        </div>
        <Separator className="mb-4" />

        <form onSubmit={handleSubmit} className="flex flex-col m-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">المعلومات الأساسية</h3>

                    <div className="space-y-2">
                        <Label htmlFor="orders_sheet_id">الورقة</Label>
                        <Select
                            value={order.orders_sheet_id || 'none'}
                            onValueChange={(value) => handleInputChange('orders_sheet_id', value === 'none' ? null : value)}
                            disabled={loading}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="اختر الورقة (اختياري)" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="none">بدون ورقة</SelectItem>
                                {activeSheets.map((sheet) => (
                                    <SelectItem key={sheet.id} value={sheet.id}>
                                        {sheet.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="customer_name">اسم العميل *</Label>
                        <Input
                            id="customer_name"
                            value={order.customer_name || ''}
                            onChange={(e) => handleInputChange('customer_name', e.target.value)}
                            required
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="customer_phone">رقم الهاتف *</Label>
                        <Input
                            id="customer_phone"
                            value={order.customer_phone || ''}
                            onChange={(e) => handleInputChange('customer_phone', e.target.value)}
                            required
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="customer_address">العنوان</Label>
                        <Textarea
                            id="customer_address"
                            value={order.customer_address || ''}
                            onChange={(e) => handleInputChange('customer_address', e.target.value)}
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="company_code">رمز/اسم الشركة</Label>
                        <div className="flex gap-2">
                            <div className="flex-1 space-y-1">
                                <Input
                                    id="company_code"
                                    placeholder="أدخل رمز أو اسم الشركة (اختياري)"
                                    value={companyCode}
                                    onChange={(e) => setCompanyCode(e.target.value)}
                                    disabled={loading}
                                />
                                <div className="text-xs text-muted-foreground">
                                    سيتم البحث عن الشركة أو إنشاؤها تلقائياً إذا لم توجد
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Financial Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">المعلومات المالية</h3>

                    <div className="space-y-2">
                        <Label htmlFor="total_price">السعر الإجمالي</Label>
                        <Input
                            id="total_price"
                            type="number"
                            step="0.01"
                            value={order.total_price || ''}
                            onChange={(e) => handleInputChange('total_price', e.target.value ? parseFloat(e.target.value) : null)}
                            disabled={loading}
                        />
                    </div>

                    {isEditing && (
                        <div className="space-y-2">
                            <Label htmlFor="final_customer_payment">الدفع النهائي</Label>
                            <Input
                                id="final_customer_payment"
                                type="number"
                                step="0.01"
                                value={order.final_customer_payment || ''}
                                onChange={(e) => handleInputChange('final_customer_payment', e.target.value ? parseFloat(e.target.value) : null)}
                                disabled={loading}
                            />
                        </div>
                    )}

                    <div className="space-y-2">
                        <Label htmlFor="commission_fixed_rate">نسبة العمولة</Label>
                        <Input
                            id="commission_fixed_rate"
                            type="number"
                            step="0.01"
                            value={order.commission_fixed_rate || ''}
                            onChange={(e) => handleInputChange('commission_fixed_rate', e.target.value ? parseFloat(e.target.value) : null)}
                            disabled={loading}
                        />
                    </div>
                </div>
            </div>

            {/* Order Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">تفاصيل الطلب</h3>

                    <div className="space-y-2">
                        <Label htmlFor="notes">ملاحظات</Label>
                        <Textarea
                            id="notes"
                            value={order.notes || ''}
                            onChange={(e) => handleInputChange('notes', e.target.value)}
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="deadline_date">تاريخ الاستحقاق</Label>
                        <Input
                            id="deadline_date"
                            type="datetime-local"
                            value={formatDateForInput(order.deadline_date)}
                            onChange={(e) => handleInputChange('deadline_date', parseDateFromInput(e.target.value))}
                            disabled={loading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="handling_status">حالة الطلب *</Label>
                        {isEditing ? (
                            <Select
                                value={order.handling_status || 'PENDING'}
                                onValueChange={(value) => handleInputChange('handling_status', value as OrderHandlingStatusSchema)}
                                disabled={loading}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="PENDING">في الانتظار</SelectItem>
                                    <SelectItem value="ASSIGNED">تم التعيين</SelectItem>
                                    <SelectItem value="PROCESSING">قيد المعالجة</SelectItem>
                                    <SelectItem value="CANCELLED">ملغي</SelectItem>
                                    <SelectItem value="DELIVERED">تم التسليم</SelectItem>
                                </SelectContent>
                            </Select>
                        ) : (
                            <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md border">
                                <span className="text-sm font-medium text-gray-700">في الانتظار</span>
                                <span className="text-xs text-gray-500">(سيتم تعيينها تلقائياً)</span>
                            </div>
                        )}
                    </div>
                </div>

                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">خيارات إضافية</h3>

                    <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="scale-110">
                            <Checkbox
                                id="breakable"
                                checked={order.breakable || false}
                                onCheckedChange={(checked) => handleInputChange('breakable', checked)}
                                disabled={loading}
                            />
                        </div>
                        <Label htmlFor="breakable" className="mr-2">قابل للكسر</Label>
                    </div>

                    {isEditing && (
                        <>
                            <div className="space-y-2">
                                <Label htmlFor="status_template">قالب حالة الطلب</Label>
                                <Select
                                    onValueChange={handleCancellationTemplateChange}
                                    disabled={loading}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="اختر قالب حالة الطلب" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {cancellationTemplates.map((template) => (
                                            <SelectItem key={template.id} value={template.id}>
                                                {template.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <p className="text-sm text-muted-foreground">
                                    اختيار قالب سيؤدي إلى تحديث حالة الطلب وإلغائه تلقائياً
                                </p>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="status_reason">سبب الحالة المخصص</Label>
                                <Textarea
                                    id="status_reason"
                                    value={order.status_reason || ''}
                                    onChange={(e) => handleInputChange('status_reason', e.target.value)}
                                    disabled={loading}
                                    placeholder="اكتب سبب الحالة يدوياً (اختياري)"
                                />
                            </div>
                        </>
                    )}
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 space-x-reverse">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate({ to: '/orders' })}
                    disabled={loading}
                >
                    إلغاء
                </Button>
                <Button type="submit" disabled={loading}>
                    {loading ? 'جاري الحفظ...' : (isEditing ? 'تحديث الطلب' : 'إنشاء الطلب')}
                </Button>
            </div>
        </form>
    </>
} 